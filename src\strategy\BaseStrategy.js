const { EventEmitter } = require('events');

/**
 * الفئة الأساسية لجميع الاستراتيجيات
 */
class BaseStrategy extends EventEmitter {
    constructor(id, name, config = {}) {
        super();
        
        this.id = id;
        this.name = name;
        this.description = config.description || name;
        this.config = config;
        this.isActive = false;
        this.lastAnalysis = null;
        this.parameters = config.parameters || {};
        
        // إعداد نظام السجلات
        this.logger = config.logger || {
            info: console.log,
            warn: console.warn,
            error: console.error,
            debug: console.debug
        };
    }

    /**
     * تفعيل الاستراتيجية
     */
    activate() {
        this.isActive = true;
        this.logger.info(`تم تفعيل الاستراتيجية: ${this.name}`);
        this.emit('activated');
    }

    /**
     * إلغاء تفعيل الاستراتيجية
     */
    deactivate() {
        this.isActive = false;
        this.logger.info(`تم إلغاء تفعيل الاستراتيجية: ${this.name}`);
        this.emit('deactivated');
    }

    /**
     * تحليل البيانات وإنتاج الإشارات
     * يجب تنفيذ هذه الدالة في الفئات المشتقة
     */
    async analyze(symbol, data) {
        throw new Error('يجب تنفيذ دالة analyze في الفئة المشتقة');
    }

    /**
     * التحقق من صحة الإشارة
     */
    validateSignal(signal) {
        if (!signal) return false;
        
        // التحقق من الحقول المطلوبة
        const requiredFields = ['symbol', 'type', 'price', 'timestamp'];
        for (const field of requiredFields) {
            if (!signal.hasOwnProperty(field)) {
                this.logger.warn(`إشارة غير صالحة: الحقل ${field} مفقود`);
                return false;
            }
        }

        // التحقق من نوع الإشارة
        if (!['CALL', 'PUT'].includes(signal.type)) {
            this.logger.warn(`نوع إشارة غير صالح: ${signal.type}`);
            return false;
        }

        return true;
    }

    /**
     * تحديث المعاملات
     */
    updateParameters(newParameters) {
        this.parameters = { ...this.parameters, ...newParameters };
        this.logger.info(`تم تحديث معاملات الاستراتيجية ${this.name}:`, newParameters);
        this.emit('parametersUpdated', this.parameters);
    }

    /**
     * الحصول على المعاملات الحالية
     */
    getParameters() {
        return { ...this.parameters };
    }

    /**
     * الحصول على معلومات الاستراتيجية
     */
    getInfo() {
        return {
            id: this.id,
            name: this.name,
            description: this.description,
            isActive: this.isActive,
            parameters: this.parameters,
            lastAnalysis: this.lastAnalysis
        };
    }

    /**
     * إعادة تعيين الاستراتيجية
     */
    reset() {
        this.lastAnalysis = null;
        this.logger.info(`تم إعادة تعيين الاستراتيجية: ${this.name}`);
        this.emit('reset');
    }

    /**
     * تسجيل نتيجة التحليل
     */
    recordAnalysis(symbol, result) {
        this.lastAnalysis = {
            symbol,
            result,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * حساب مؤشرات فنية بسيطة
     */
    calculateSMA(prices, period) {
        if (!prices || prices.length < period) return null;
        
        const sum = prices.slice(-period).reduce((a, b) => a + b, 0);
        return sum / period;
    }

    /**
     * حساب المتوسط المتحرك الأسي
     */
    calculateEMA(prices, period) {
        if (!prices || prices.length === 0) return null;
        
        const multiplier = 2 / (period + 1);
        let ema = prices[0];
        
        for (let i = 1; i < prices.length; i++) {
            ema = (prices[i] * multiplier) + (ema * (1 - multiplier));
        }
        
        return ema;
    }

    /**
     * حساب مؤشر القوة النسبية (RSI)
     */
    calculateRSI(prices, period = 14) {
        if (!prices || prices.length < period + 1) return null;
        
        let gains = 0;
        let losses = 0;
        
        // حساب المتوسط الأولي للمكاسب والخسائر
        for (let i = 1; i <= period; i++) {
            const change = prices[i] - prices[i - 1];
            if (change > 0) {
                gains += change;
            } else {
                losses -= change;
            }
        }
        
        let avgGain = gains / period;
        let avgLoss = losses / period;
        
        // حساب RSI للفترات المتبقية
        for (let i = period + 1; i < prices.length; i++) {
            const change = prices[i] - prices[i - 1];
            const gain = change > 0 ? change : 0;
            const loss = change < 0 ? -change : 0;
            
            avgGain = (avgGain * (period - 1) + gain) / period;
            avgLoss = (avgLoss * (period - 1) + loss) / period;
        }
        
        if (avgLoss === 0) return 100;
        
        const rs = avgGain / avgLoss;
        return 100 - (100 / (1 + rs));
    }

    /**
     * تحديد الاتجاه
     */
    determineTrend(prices, shortPeriod = 5, longPeriod = 20) {
        if (!prices || prices.length < longPeriod) return 'UNKNOWN';
        
        const shortMA = this.calculateSMA(prices, shortPeriod);
        const longMA = this.calculateSMA(prices, longPeriod);
        
        if (!shortMA || !longMA) return 'UNKNOWN';
        
        if (shortMA > longMA) return 'UPTREND';
        if (shortMA < longMA) return 'DOWNTREND';
        return 'SIDEWAYS';
    }

    /**
     * حساب التقلبات (Volatility)
     */
    calculateVolatility(prices, period = 20) {
        if (!prices || prices.length < period) return null;
        
        const recentPrices = prices.slice(-period);
        const mean = recentPrices.reduce((a, b) => a + b, 0) / period;
        
        const variance = recentPrices.reduce((sum, price) => {
            return sum + Math.pow(price - mean, 2);
        }, 0) / period;
        
        return Math.sqrt(variance);
    }
}

module.exports = { BaseStrategy };
