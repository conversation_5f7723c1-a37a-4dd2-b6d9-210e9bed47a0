/**
 * إدارة إعدادات الروبوت
 */

const fs = require('fs').promises;
const path = require('path');

class Config {
    constructor() {
        this.config = {};
        this.configPath = path.join(__dirname, '../../config.json');
        this.defaultConfig = {
            // إعدادات الاتصال
            connection: {
                url: 'https://qxbroker.com/en/demo-trade',
                timeout: 60000,
                retryAttempts: 3,
                retryDelay: 5000
            },
            
            // إعدادات التداول
            trading: {
                defaultAmount: 1,
                maxAmount: 100,
                minAmount: 1,
                defaultExpiration: 60, // بالثواني
                maxConcurrentTrades: 5,
                riskPercentage: 2, // نسبة المخاطرة من الرصيد
                stopLoss: 50, // حد الخسارة اليومي
                takeProfit: 100 // هدف الربح اليومي
            },
            
            // إعدادات البيانات
            data: {
                historicalCandles: 500,
                timeframe: '5m', // 5 دقائق
                updateInterval: 1000, // ميلي ثانية
                saveToFile: true,
                dataPath: './data'
            },
            
            // إعدادات السجلات
            logging: {
                level: 'info',
                file: './logs/trading.log',
                maxSize: '10m',
                maxFiles: 5,
                console: true
            },
            
            // إعدادات الأمان
            security: {
                enableStealth: true,
                userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                viewport: {
                    width: 1920,
                    height: 1080
                }
            },
            
            // إعدادات الاستراتيجية
            strategy: {
                name: 'simple_test',
                enabled: true,
                parameters: {}
            },
            
            // إعدادات الإشعارات
            notifications: {
                enabled: false,
                webhook: '',
                events: ['trade_opened', 'trade_closed', 'error']
            }
        };
    }

    /**
     * تحميل الإعدادات من الملف
     */
    async load() {
        try {
            const configExists = await this.fileExists(this.configPath);
            
            if (configExists) {
                const configData = await fs.readFile(this.configPath, 'utf8');
                this.config = { ...this.defaultConfig, ...JSON.parse(configData) };
            } else {
                this.config = { ...this.defaultConfig };
                await this.save();
            }
            
            return this.config;
        } catch (error) {
            console.warn('⚠️ فشل في تحميل الإعدادات، استخدام الإعدادات الافتراضية:', error.message);
            this.config = { ...this.defaultConfig };
            return this.config;
        }
    }

    /**
     * حفظ الإعدادات في الملف
     */
    async save() {
        try {
            const configDir = path.dirname(this.configPath);
            await fs.mkdir(configDir, { recursive: true });
            await fs.writeFile(this.configPath, JSON.stringify(this.config, null, 2));
        } catch (error) {
            console.error('❌ فشل في حفظ الإعدادات:', error);
        }
    }

    /**
     * الحصول على قيمة إعداد
     */
    get(key) {
        const keys = key.split('.');
        let value = this.config;
        
        for (const k of keys) {
            if (value && typeof value === 'object' && k in value) {
                value = value[k];
            } else {
                return undefined;
            }
        }
        
        return value;
    }

    /**
     * تعيين قيمة إعداد
     */
    set(key, value) {
        const keys = key.split('.');
        let current = this.config;
        
        for (let i = 0; i < keys.length - 1; i++) {
            const k = keys[i];
            if (!(k in current) || typeof current[k] !== 'object') {
                current[k] = {};
            }
            current = current[k];
        }
        
        current[keys[keys.length - 1]] = value;
    }

    /**
     * الحصول على جميع الإعدادات
     */
    getAll() {
        return { ...this.config };
    }

    /**
     * التحقق من وجود الملف
     */
    async fileExists(filePath) {
        try {
            await fs.access(filePath);
            return true;
        } catch {
            return false;
        }
    }
}

module.exports = { Config };
