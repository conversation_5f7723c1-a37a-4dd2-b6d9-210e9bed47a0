const { BaseStrategy } = require('../BaseStrategy');

/**
 * استراتيجية مخصصة بسيطة
 */
class CustomStrategy extends BaseStrategy {
    constructor(config = {}) {
        super('custom', 'استراتيجية مخصصة', config);
        
        // إعدادات الاستراتيجية
        this.settings = {
            minPriceChange: config.minPriceChange || 0.001, // الحد الأدنى لتغيير السعر
            timeframe: config.timeframe || '5m', // الإطار الزمني
            riskPercentage: config.riskPercentage || 2, // نسبة المخاطرة
            ...config
        };

        this.logger.info(`تم تهيئة الاستراتيجية المخصصة بالإعدادات: ${JSON.stringify(this.settings)}`);
    }

    /**
     * تحليل السوق وإنتاج الإشارات
     */
    async analyze(symbol, data) {
        try {
            // التحقق من توفر البيانات المطلوبة
            if (!data || !data.currentPrice) {
                return null;
            }

            const currentPrice = data.currentPrice;
            const previousPrice = data.previousPrice;
            
            // حساب تغيير السعر
            if (!previousPrice) {
                return null;
            }

            const priceChange = currentPrice - previousPrice;
            const priceChangePercent = (priceChange / previousPrice) * 100;

            // تحليل بسيط: إشارة صعود إذا كان التغيير إيجابي وكبير بما فيه الكفاية
            if (Math.abs(priceChangePercent) >= this.settings.minPriceChange) {
                const signal = {
                    symbol: symbol,
                    type: priceChange > 0 ? 'CALL' : 'PUT',
                    confidence: Math.min(Math.abs(priceChangePercent) * 10, 100),
                    price: currentPrice,
                    timestamp: new Date().toISOString(),
                    reason: `تغيير السعر: ${priceChangePercent.toFixed(4)}%`,
                    timeframe: this.settings.timeframe,
                    riskLevel: this.calculateRiskLevel(priceChangePercent)
                };

                this.logger.info(`📊 إشارة جديدة لـ ${symbol}:`, {
                    type: signal.type,
                    confidence: signal.confidence,
                    reason: signal.reason
                });

                return signal;
            }

            return null;

        } catch (error) {
            this.logger.error(`خطأ في تحليل ${symbol}:`, error);
            return null;
        }
    }

    /**
     * حساب مستوى المخاطرة
     */
    calculateRiskLevel(priceChangePercent) {
        const absChange = Math.abs(priceChangePercent);
        
        if (absChange < 0.01) return 'LOW';
        if (absChange < 0.05) return 'MEDIUM';
        return 'HIGH';
    }

    /**
     * تحديد حجم الصفقة
     */
    calculateTradeSize(balance, signal) {
        try {
            // حساب حجم الصفقة بناءً على نسبة المخاطرة
            const riskAmount = balance * (this.settings.riskPercentage / 100);
            
            // تعديل الحجم بناءً على مستوى الثقة
            const confidenceMultiplier = signal.confidence / 100;
            const tradeSize = riskAmount * confidenceMultiplier;

            // التأكد من الحد الأدنى والأقصى
            const minTradeSize = 1;
            const maxTradeSize = balance * 0.1; // حد أقصى 10% من الرصيد

            return Math.max(minTradeSize, Math.min(tradeSize, maxTradeSize));

        } catch (error) {
            this.logger.error('خطأ في حساب حجم الصفقة:', error);
            return 1; // حجم افتراضي
        }
    }

    /**
     * التحقق من صحة الإشارة
     */
    validateSignal(signal) {
        if (!signal) return false;
        
        // التحقق من الحقول المطلوبة
        if (!signal.symbol || !signal.type || !signal.price) {
            return false;
        }

        // التحقق من نوع الإشارة
        if (!['CALL', 'PUT'].includes(signal.type)) {
            return false;
        }

        // التحقق من مستوى الثقة
        if (signal.confidence < 50) {
            this.logger.debug(`إشارة مرفوضة بسبب انخفاض الثقة: ${signal.confidence}%`);
            return false;
        }

        return true;
    }

    /**
     * الحصول على إعدادات الاستراتيجية
     */
    getSettings() {
        return { ...this.settings };
    }

    /**
     * تحديث إعدادات الاستراتيجية
     */
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        this.logger.info(`تم تحديث إعدادات الاستراتيجية: ${JSON.stringify(newSettings)}`);
    }

    /**
     * إعادة تعيين الاستراتيجية
     */
    reset() {
        this.logger.info('تم إعادة تعيين الاستراتيجية المخصصة');
        // يمكن إضافة منطق إعادة التعيين هنا إذا لزم الأمر
    }

    /**
     * الحصول على إحصائيات الاستراتيجية
     */
    getStatistics() {
        return {
            name: this.name,
            description: this.description,
            settings: this.settings,
            isActive: this.isActive,
            lastAnalysis: this.lastAnalysis
        };
    }
}

module.exports = { CustomStrategy };
