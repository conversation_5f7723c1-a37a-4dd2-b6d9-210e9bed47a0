{"instruments": [{"symbol": "ADAUSD", "name": "Cardano", "type": "cryptocurrency", "isActive": true, "profitPercentage": 80, "minAmount": 1, "maxAmount": 1000, "expirationTimes": [30, 60, 120, 180, 300, 600, 900, 1800, 3600], "source": "default", "lastUpdate": "2025-07-11T16:56:19.016Z"}, {"symbol": "AUDUSD", "name": "AUD/USD", "type": "currency", "isActive": true, "profitPercentage": 85, "minAmount": 1, "maxAmount": 1000, "expirationTimes": [30, 60, 120, 180, 300, 600, 900, 1800, 3600], "source": "default", "lastUpdate": "2025-07-11T16:56:19.016Z"}, {"symbol": "BTCUSD", "name": "Bitcoin", "type": "cryptocurrency", "isActive": true, "profitPercentage": 80, "minAmount": 1, "maxAmount": 1000, "expirationTimes": [30, 60, 120, 180, 300, 600, 900, 1800, 3600], "source": "default", "lastUpdate": "2025-07-11T16:56:19.016Z"}, {"symbol": "DOGUSD", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "cryptocurrency", "isActive": true, "profitPercentage": 80, "minAmount": 1, "maxAmount": 1000, "expirationTimes": [30, 60, 120, 180, 300, 600, 900, 1800, 3600], "source": "default", "lastUpdate": "2025-07-11T16:56:19.016Z"}, {"symbol": "ETHUSD", "name": "Ethereum", "type": "cryptocurrency", "isActive": true, "profitPercentage": 80, "minAmount": 1, "maxAmount": 1000, "expirationTimes": [30, 60, 120, 180, 300, 600, 900, 1800, 3600], "source": "default", "lastUpdate": "2025-07-11T16:56:19.016Z"}, {"symbol": "EURUSD", "name": "EUR/USD", "type": "currency", "isActive": true, "profitPercentage": 85, "minAmount": 1, "maxAmount": 1000, "expirationTimes": [30, 60, 120, 180, 300, 600, 900, 1800, 3600], "source": "default", "lastUpdate": "2025-07-11T16:56:19.016Z"}, {"symbol": "GBP/USD", "name": "GBP/USD", "type": "currency", "isActive": true, "profitPercentage": 85, "minAmount": 1, "maxAmount": 1000, "expirationTimes": [30, 60, 120, 180, 300, 600, 900, 1800, 3600], "source": "general-search", "lastUpdate": "2025-07-11T16:56:19.016Z"}, {"symbol": "GBPUSD", "name": "GBP/USD", "type": "currency", "isActive": true, "profitPercentage": 85, "minAmount": 1, "maxAmount": 1000, "expirationTimes": [30, 60, 120, 180, 300, 600, 900, 1800, 3600], "source": "default", "lastUpdate": "2025-07-11T16:56:19.016Z"}, {"symbol": "LTCUSD", "name": "Litecoin", "type": "cryptocurrency", "isActive": true, "profitPercentage": 80, "minAmount": 1, "maxAmount": 1000, "expirationTimes": [30, 60, 120, 180, 300, 600, 900, 1800, 3600], "source": "default", "lastUpdate": "2025-07-11T16:56:19.016Z"}, {"symbol": "USDCAD", "name": "USD/CAD", "type": "currency", "isActive": true, "profitPercentage": 85, "minAmount": 1, "maxAmount": 1000, "expirationTimes": [30, 60, 120, 180, 300, 600, 900, 1800, 3600], "source": "default", "lastUpdate": "2025-07-11T16:56:19.016Z"}, {"symbol": "USDCHF", "name": "USD/CHF", "type": "currency", "isActive": true, "profitPercentage": 85, "minAmount": 1, "maxAmount": 1000, "expirationTimes": [30, 60, 120, 180, 300, 600, 900, 1800, 3600], "source": "default", "lastUpdate": "2025-07-11T16:56:19.016Z"}, {"symbol": "USDJPY", "name": "USD/JPY", "type": "currency", "isActive": true, "profitPercentage": 85, "minAmount": 1, "maxAmount": 1000, "expirationTimes": [30, 60, 120, 180, 300, 600, 900, 1800, 3600], "source": "default", "lastUpdate": "2025-07-11T16:56:19.016Z"}], "lastUpdate": "2025-07-11T16:56:19.042Z", "count": 12}