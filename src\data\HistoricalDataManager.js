/**
 * مدير البيانات التاريخية
 */

const { EventEmitter } = require('events');
const fs = require('fs').promises;
const path = require('path');

class HistoricalDataManager extends EventEmitter {
    constructor(config, logger) {
        super();
        this.config = config;
        this.logger = logger;
        this.connection = null;
        this.historicalData = new Map(); // symbol -> data
        this.dataPath = config.get('data.dataPath') || './data';
        this.candleCount = config.get('data.historicalCandles') || 500;
        this.timeframe = config.get('data.timeframe') || '5m';
    }

    /**
     * تهيئة مدير البيانات التاريخية
     */
    async initialize(connection) {
        try {
            this.connection = connection;
            
            // إنشاء مجلد البيانات
            await this.ensureDataDirectory();
            
            // تحميل البيانات المحفوظة
            await this.loadStoredData();
            
            this.logger.info('✅ تم تهيئة مدير البيانات التاريخية بنجاح');
            
        } catch (error) {
            this.logger.error('❌ فشل في تهيئة مدير البيانات التاريخية:', error);
            throw error;
        }
    }

    /**
     * التأكد من وجود مجلد البيانات
     */
    async ensureDataDirectory() {
        try {
            const historicalDir = path.join(this.dataPath, 'historical');
            await fs.mkdir(historicalDir, { recursive: true });
        } catch (error) {
            // تجاهل الخطأ إذا كان المجلد موجوداً
        }
    }

    /**
     * تحميل البيانات المحفوظة
     */
    async loadStoredData() {
        try {
            const historicalDir = path.join(this.dataPath, 'historical');
            const files = await fs.readdir(historicalDir);
            
            for (const file of files) {
                if (file.endsWith('.json')) {
                    const symbol = file.replace('.json', '');
                    const filePath = path.join(historicalDir, file);
                    const data = await fs.readFile(filePath, 'utf8');
                    const candleData = JSON.parse(data);
                    
                    this.historicalData.set(symbol, candleData);
                }
            }
            
            this.logger.info(`📥 تم تحميل البيانات التاريخية لـ ${this.historicalData.size} رمز`);
            
        } catch (error) {
            this.logger.info('📝 لا توجد بيانات تاريخية محفوظة');
        }
    }

    /**
     * جلب البيانات التاريخية لرمز معين
     */
    async loadHistoricalData(symbol) {
        try {
            this.logger.info(`📊 جلب البيانات التاريخية لـ ${symbol}...`);
            
            // محاولة جلب البيانات من المنصة
            const candles = await this.fetchCandlesFromPlatform(symbol);
            
            if (candles && candles.length > 0) {
                this.historicalData.set(symbol, {
                    symbol: symbol,
                    timeframe: this.timeframe,
                    candles: candles,
                    lastUpdate: new Date().toISOString(),
                    count: candles.length
                });
                
                // حفظ البيانات
                await this.saveSymbolData(symbol);
                
                this.emit('dataUpdate', { symbol, candles });
                this.logger.info(`✅ تم جلب ${candles.length} شمعة لـ ${symbol}`);
                
                return candles;
            } else {
                // عدم إنشاء بيانات وهمية - انتظار البيانات الحقيقية
                this.logger.warn(`⚠️ لم يتم العثور على بيانات تاريخية لـ ${symbol} - انتظار البيانات الحقيقية من WebSocket`);

                // إنشاء مدخل فارغ للرمز
                this.historicalData.set(symbol, {
                    symbol: symbol,
                    timeframe: this.timeframe,
                    candles: [],
                    lastUpdate: new Date().toISOString(),
                    count: 0,
                    waitingForData: true
                });

                return [];
            }
            
        } catch (error) {
            this.logger.error(`❌ فشل في جلب البيانات التاريخية لـ ${symbol}:`, error);
            throw error;
        }
    }

    /**
     * تحديث البيانات التاريخية من WebSocket
     */
    updateHistoricalDataFromWebSocket(symbol, candles) {
        try {
            if (!Array.isArray(candles) || candles.length === 0) {
                return;
            }

            // تنسيق البيانات
            const formattedCandles = candles.map(candle => ({
                time: new Date(candle.time * 1000).toISOString(),
                open: parseFloat(candle.open),
                high: parseFloat(candle.high),
                low: parseFloat(candle.low),
                close: parseFloat(candle.close),
                volume: parseInt(candle.volume || 0)
            }));

            // تحديث البيانات المحفوظة
            this.historicalData.set(symbol, {
                symbol: symbol,
                timeframe: this.timeframe,
                candles: formattedCandles,
                lastUpdate: new Date().toISOString(),
                count: formattedCandles.length,
                isReal: true
            });

            // حفظ البيانات
            this.saveSymbolData(symbol);

            this.logger.info(`✅ تم تحديث البيانات التاريخية لـ ${symbol}: ${formattedCandles.length} شمعة حقيقية`);
            this.emit('dataUpdate', { symbol, candles: formattedCandles });

            return formattedCandles;

        } catch (error) {
            this.logger.error(`خطأ في تحديث البيانات التاريخية لـ ${symbol}:`, error);
            return null;
        }
    }

    /**
     * جلب الشموع من المنصة
     */
    async fetchCandlesFromPlatform(symbol) {
        if (!this.connection || !this.connection.isConnected()) {
            throw new Error('لا يوجد اتصال نشط');
        }

        try {
            // انتظار تحميل الرسم البياني
            await this.connection.page.waitForTimeout(3000);
            
            const candles = await this.connection.evaluate((symbol, timeframe, count) => {
                // محاولة الوصول للبيانات من مصادر مختلفة
                
                // 1. البحث في window objects
                const possibleSources = [
                    'chartData',
                    'candleData',
                    'historicalData',
                    'priceData',
                    'ohlcData'
                ];
                
                for (const source of possibleSources) {
                    if (window[source]) {
                        const data = window[source];
                        if (Array.isArray(data) && data.length > 0) {
                            return this.formatCandleData(data, symbol);
                        }
                    }
                }
                
                // 2. البحث في canvas elements (TradingView style)
                const canvases = document.querySelectorAll('canvas');
                for (const canvas of canvases) {
                    if (canvas.width > 500 && canvas.height > 200) {
                        // هذا على الأرجح رسم بياني
                        // محاولة استخراج البيانات من context
                        try {
                            const ctx = canvas.getContext('2d');
                            if (ctx && ctx.getImageData) {
                                // يمكن تحليل البيانات من الصورة لكن هذا معقد
                                // سنتركه للمرحلة التالية
                            }
                        } catch (e) {}
                    }
                }
                
                // 3. البحث في localStorage
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key.includes('chart') || key.includes('candle') || key.includes('price')) {
                        try {
                            const data = JSON.parse(localStorage.getItem(key));
                            if (Array.isArray(data) && data.length > 10) {
                                return this.formatCandleData(data, symbol);
                            }
                        } catch (e) {}
                    }
                }
                
                // 4. البحث في sessionStorage
                for (let i = 0; i < sessionStorage.length; i++) {
                    const key = sessionStorage.key(i);
                    if (key.includes('chart') || key.includes('candle') || key.includes('price')) {
                        try {
                            const data = JSON.parse(sessionStorage.getItem(key));
                            if (Array.isArray(data) && data.length > 10) {
                                return this.formatCandleData(data, symbol);
                            }
                        } catch (e) {}
                    }
                }
                
                return null;
            }, symbol, this.timeframe, this.candleCount);

            return candles;
            
        } catch (error) {
            this.logger.error(`فشل في جلب الشموع من المنصة لـ ${symbol}:`, error);
            return null;
        }
    }

    /**
     * إنشاء بيانات وهمية للاختبار
     */
    generateDummyCandles(symbol) {
        const candles = [];
        const now = new Date();
        const timeframeMs = this.getTimeframeInMs(this.timeframe);
        
        // سعر أساسي حسب نوع الرمز
        let basePrice = 1.1000; // EUR/USD default
        if (symbol.includes('BTC')) basePrice = 45000;
        else if (symbol.includes('ETH')) basePrice = 3000;
        else if (symbol.includes('JPY')) basePrice = 110;
        else if (symbol.includes('GBP')) basePrice = 1.3000;
        
        for (let i = this.candleCount - 1; i >= 0; i--) {
            const timestamp = new Date(now.getTime() - (i * timeframeMs));
            
            // إنشاء تغيير عشوائي صغير
            const change = (Math.random() - 0.5) * 0.01; // تغيير 1%
            const open = basePrice * (1 + change);
            const close = open * (1 + (Math.random() - 0.5) * 0.005); // تغيير 0.5%
            const high = Math.max(open, close) * (1 + Math.random() * 0.003);
            const low = Math.min(open, close) * (1 - Math.random() * 0.003);
            const volume = Math.random() * 1000 + 100;
            
            candles.push({
                timestamp: timestamp.toISOString(),
                open: parseFloat(open.toFixed(symbol.includes('JPY') ? 3 : 5)),
                high: parseFloat(high.toFixed(symbol.includes('JPY') ? 3 : 5)),
                low: parseFloat(low.toFixed(symbol.includes('JPY') ? 3 : 5)),
                close: parseFloat(close.toFixed(symbol.includes('JPY') ? 3 : 5)),
                volume: Math.round(volume)
            });
            
            basePrice = close; // استخدام سعر الإغلاق كأساس للشمعة التالية
        }
        
        return candles;
    }

    /**
     * تحويل الإطار الزمني إلى ميلي ثانية
     */
    getTimeframeInMs(timeframe) {
        const timeframes = {
            '1m': 60 * 1000,
            '5m': 5 * 60 * 1000,
            '15m': 15 * 60 * 1000,
            '30m': 30 * 60 * 1000,
            '1h': 60 * 60 * 1000,
            '4h': 4 * 60 * 60 * 1000,
            '1d': 24 * 60 * 60 * 1000
        };
        return timeframes[timeframe] || timeframes['5m'];
    }

    /**
     * الحصول على البيانات التاريخية
     */
    getHistoricalData(symbol, timeframe = null, count = null) {
        const data = this.historicalData.get(symbol);
        if (!data) return null;
        
        let candles = data.candles;
        
        // تطبيق العدد المطلوب
        if (count && count < candles.length) {
            candles = candles.slice(-count);
        }
        
        return {
            symbol: symbol,
            timeframe: data.timeframe,
            candles: candles,
            count: candles.length,
            lastUpdate: data.lastUpdate
        };
    }

    /**
     * الحصول على آخر شمعة
     */
    getLatestCandle(symbol) {
        const data = this.historicalData.get(symbol);
        if (!data || !data.candles || data.candles.length === 0) {
            return null;
        }
        
        return data.candles[data.candles.length - 1];
    }

    /**
     * حفظ بيانات رمز معين
     */
    async saveSymbolData(symbol) {
        try {
            const data = this.historicalData.get(symbol);
            if (!data) return;
            
            const historicalDir = path.join(this.dataPath, 'historical');
            const filePath = path.join(historicalDir, `${symbol}.json`);
            
            await fs.writeFile(filePath, JSON.stringify(data, null, 2));
            
        } catch (error) {
            this.logger.error(`فشل في حفظ البيانات التاريخية لـ ${symbol}:`, error);
        }
    }

    /**
     * حفظ جميع البيانات
     */
    async saveData() {
        try {
            for (const symbol of this.historicalData.keys()) {
                await this.saveSymbolData(symbol);
            }
            
            this.logger.info('✅ تم حفظ جميع البيانات التاريخية');
            
        } catch (error) {
            this.logger.error('❌ فشل في حفظ البيانات التاريخية:', error);
        }
    }

    /**
     * الحصول على حالة مدير البيانات التاريخية
     */
    getStatus() {
        const symbols = Array.from(this.historicalData.keys());
        const totalCandles = symbols.reduce((total, symbol) => {
            const data = this.historicalData.get(symbol);
            return total + (data.candles ? data.candles.length : 0);
        }, 0);

        return {
            symbolsCount: symbols.length,
            totalCandles: totalCandles,
            symbols: symbols,
            timeframe: this.timeframe,
            targetCandleCount: this.candleCount
        };
    }

    /**
     * تحديث البيانات لرمز معين
     */
    async refreshSymbolData(symbol) {
        await this.loadHistoricalData(symbol);
    }

    /**
     * تحديث جميع البيانات
     */
    async refreshAllData() {
        const symbols = Array.from(this.historicalData.keys());
        for (const symbol of symbols) {
            await this.refreshSymbolData(symbol);
        }
    }
}

module.exports = { HistoricalDataManager };
