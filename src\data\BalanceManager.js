/**
 * مدير الرصيد
 */

const { EventEmitter } = require('events');
const fs = require('fs').promises;
const path = require('path');

class BalanceManager extends EventEmitter {
    constructor(config, logger) {
        super();
        this.config = config;
        this.logger = logger;
        this.connection = null;
        this.currentBalance = null;
        this.balanceHistory = [];
        this.isMonitoring = false;
        this.monitoringInterval = null;
        this.updateInterval = config.get('data.updateInterval') || 5000;
        this.dataPath = config.get('data.dataPath') || './data';
    }

    /**
     * تهيئة مدير الرصيد
     */
    async initialize(connection) {
        try {
            this.connection = connection;
            
            // إنشاء مجلد البيانات
            await this.ensureDataDirectory();
            
            // تحميل بيانات الرصيد المحفوظة
            await this.loadStoredData();
            
            this.logger.info('✅ تم تهيئة مدير الرصيد بنجاح');
            
        } catch (error) {
            this.logger.error('❌ فشل في تهيئة مدير الرصيد:', error);
            throw error;
        }
    }

    /**
     * التأكد من وجود مجلد البيانات
     */
    async ensureDataDirectory() {
        try {
            await fs.mkdir(this.dataPath, { recursive: true });
        } catch (error) {
            // تجاهل الخطأ إذا كان المجلد موجوداً
        }
    }

    /**
     * تحميل البيانات المحفوظة
     */
    async loadStoredData() {
        try {
            const balanceFile = path.join(this.dataPath, 'balance.json');
            const data = await fs.readFile(balanceFile, 'utf8');
            const balanceData = JSON.parse(data);
            
            this.currentBalance = balanceData.currentBalance;
            this.balanceHistory = balanceData.history || [];
            
            this.logger.info('📥 تم تحميل بيانات الرصيد المحفوظة');
            
        } catch (error) {
            this.logger.info('📝 لا توجد بيانات رصيد محفوظة، بدء جديد');
            this.balanceHistory = [];
        }
    }

    /**
     * بدء مراقبة الرصيد
     */
    async startMonitoring() {
        if (this.isMonitoring) {
            this.logger.warn('⚠️ مراقبة الرصيد نشطة بالفعل');
            return;
        }

        try {
            this.logger.info('👁️ بدء مراقبة الرصيد...');
            
            // جلب الرصيد الأولي
            await this.updateBalance();
            
            // بدء المراقبة الدورية
            this.monitoringInterval = setInterval(async () => {
                try {
                    await this.updateBalance();
                } catch (error) {
                    this.logger.error('خطأ في تحديث الرصيد:', error);
                }
            }, this.updateInterval);
            
            this.isMonitoring = true;
            this.logger.info('✅ تم بدء مراقبة الرصيد بنجاح');
            
        } catch (error) {
            this.logger.error('❌ فشل في بدء مراقبة الرصيد:', error);
            throw error;
        }
    }

    /**
     * إيقاف مراقبة الرصيد
     */
    async stopMonitoring() {
        if (!this.isMonitoring) {
            return;
        }

        try {
            if (this.monitoringInterval) {
                clearInterval(this.monitoringInterval);
                this.monitoringInterval = null;
            }
            
            this.isMonitoring = false;
            
            // حفظ البيانات النهائية
            await this.saveData();
            
            this.logger.info('✅ تم إيقاف مراقبة الرصيد');
            
        } catch (error) {
            this.logger.error('❌ خطأ في إيقاف مراقبة الرصيد:', error);
        }
    }

    /**
     * تحديث الرصيد
     */
    async updateBalance() {
        try {
            const newBalance = await this.fetchBalance();
            
            if (newBalance !== null && newBalance !== this.currentBalance) {
                const previousBalance = this.currentBalance;
                this.currentBalance = newBalance;
                
                // إضافة إلى التاريخ
                this.balanceHistory.push({
                    balance: newBalance,
                    timestamp: new Date().toISOString(),
                    change: previousBalance ? newBalance - previousBalance : 0
                });

                // الاحتفاظ بآخر 1000 سجل فقط
                if (this.balanceHistory.length > 1000) {
                    this.balanceHistory = this.balanceHistory.slice(-1000);
                }
                
                this.emit('balanceUpdate', newBalance);
                this.logger.info(`💰 تحديث الرصيد: $${newBalance}`);
                
                // حفظ البيانات
                await this.saveData();
            }
            
        } catch (error) {
            this.logger.error('خطأ في تحديث الرصيد:', error);
            this.emit('error', error);
        }
    }

    /**
     * جلب الرصيد من المنصة
     */
    async fetchBalance() {
        if (!this.connection || !this.connection.isConnected()) {
            throw new Error('لا يوجد اتصال نشط');
        }

        try {
            // البحث عن الرصيد في واجهة المستخدم
            const balance = await this.connection.evaluate(() => {
                // البحث عن عنصر الرصيد بطرق مختلفة خاصة بـ Quotex
                const selectors = [
                    // Quotex specific selectors
                    '.balance__value',
                    '.balance-value',
                    '.user-balance__value',
                    '.account-balance__amount',
                    '.demo-balance__value',
                    '.real-balance__value',
                    '[data-qa="balance"]',
                    '[data-testid="balance"]',
                    '.balance',
                    '.account-balance',
                    '.user-balance',
                    '[data-balance]',
                    '.balance-amount',
                    '.wallet-balance',
                    '.demo-balance',
                    '.real-balance'
                ];

                // البحث في العناصر المحددة
                for (const selector of selectors) {
                    const elements = document.querySelectorAll(selector);
                    for (const element of elements) {
                        const text = element.textContent || element.innerText;
                        if (text) {
                            // البحث عن أرقام مع أو بدون رموز العملة
                            const matches = text.match(/(\$|USD|€|EUR)?\s*([0-9,]+\.?[0-9]*)/g);
                            if (matches) {
                                for (const match of matches) {
                                    const numMatch = match.match(/([0-9,]+\.?[0-9]*)/);
                                    if (numMatch) {
                                        const value = parseFloat(numMatch[1].replace(/,/g, ''));
                                        if (value > 100 && value < 1000000) { // رصيد معقول
                                            console.log(`Found balance in ${selector}: ${value}`);
                                            return value;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                // البحث المحدد في Quotex - بناءً على الصورة
                // البحث عن النص الذي يحتوي على الرصيد في الأعلى
                const topElements = document.querySelectorAll('div, span, p');
                for (const element of topElements) {
                    const text = element.textContent || element.innerText;
                    if (text && text.includes('$') && text.includes(',')) {
                        // البحث عن نمط مثل $10,815.09
                        const balanceMatch = text.match(/\$([0-9,]+\.?[0-9]*)/);
                        if (balanceMatch) {
                            const value = parseFloat(balanceMatch[1].replace(/,/g, ''));
                            if (value > 100) {
                                console.log(`Found Quotex balance: ${value} from text: ${text}`);
                                return value;
                            }
                        }
                    }
                }

                // البحث في جميع العناصر المرئية
                const allElements = document.querySelectorAll('*');
                for (const element of allElements) {
                    const text = element.textContent || element.innerText;
                    if (text && text.length < 50) { // تجنب النصوص الطويلة
                        // البحث عن أنماط الرصيد المختلفة
                        const patterns = [
                            /\$\s*([0-9,]+\.?[0-9]*)/,
                            /([0-9,]+\.?[0-9]*)\s*\$/,
                            /USD\s*([0-9,]+\.?[0-9]*)/,
                            /([0-9,]+\.?[0-9]*)\s*USD/,
                            /Balance[:\s]*\$?([0-9,]+\.?[0-9]*)/i,
                            /Demo[:\s]*\$?([0-9,]+\.?[0-9]*)/i
                        ];

                        for (const pattern of patterns) {
                            const match = text.match(pattern);
                            if (match) {
                                const value = parseFloat(match[1].replace(/,/g, ''));
                                if (value > 0 && value < 1000000) {
                                    console.log(`Found balance with pattern: ${value} from text: ${text}`);
                                    return value;
                                }
                            }
                        }
                    }
                }

                // البحث في localStorage و sessionStorage
                const storages = [localStorage, sessionStorage];
                for (const storage of storages) {
                    for (let i = 0; i < storage.length; i++) {
                        const key = storage.key(i);
                        const value = storage.getItem(key);
                        if (key && key.toLowerCase().includes('balance') && value) {
                            try {
                                const parsed = JSON.parse(value);
                                if (typeof parsed === 'number' && parsed > 0) {
                                    console.log(`Found balance in ${storage === localStorage ? 'localStorage' : 'sessionStorage'}: ${parsed}`);
                                    return parsed;
                                }
                                if (parsed.balance && typeof parsed.balance === 'number') {
                                    console.log(`Found balance object in storage: ${parsed.balance}`);
                                    return parsed.balance;
                                }
                            } catch (e) {
                                const numValue = parseFloat(value);
                                if (!isNaN(numValue) && numValue > 0) {
                                    console.log(`Found balance as string in storage: ${numValue}`);
                                    return numValue;
                                }
                            }
                        }
                    }
                }

                console.log('No balance found');
                return null;
            });

            return balance;
            
        } catch (error) {
            this.logger.error('فشل في جلب الرصيد:', error);
            return null;
        }
    }

    /**
     * الحصول على الرصيد الحالي
     */
    async getCurrentBalance() {
        if (this.currentBalance === null) {
            await this.updateBalance();
        }
        return this.currentBalance;
    }

    /**
     * الحصول على الرصيد المخزن محلياً
     */
    getStoredBalance() {
        return this.currentBalance;
    }

    /**
     * الحصول على تاريخ الرصيد
     */
    getBalanceHistory() {
        return [...this.balanceHistory];
    }

    /**
     * تحديث الرصيد يدوياً
     */
    async refresh() {
        await this.updateBalance();
    }

    /**
     * تحديث الرصيد من بيانات WebSocket
     */
    updateBalance(balanceData) {
        try {
            if (balanceData && typeof balanceData.balance !== 'undefined') {
                const newBalance = parseFloat(balanceData.balance);
                const previousBalance = this.currentBalance;

                this.currentBalance = newBalance;
                this.currency = balanceData.currency || this.currency || 'USD';
                this.isDemo = balanceData.isDemo !== undefined ? balanceData.isDemo : this.isDemo;
                this.lastUpdate = new Date().toISOString();

                // حفظ البيانات
                this.saveBalanceData();

                // إرسال حدث التحديث
                this.emit('balanceUpdate', {
                    balance: this.currentBalance,
                    previousBalance: previousBalance,
                    change: this.currentBalance - (previousBalance || 0),
                    currency: this.currency,
                    isDemo: this.isDemo,
                    timestamp: this.lastUpdate
                });

                this.logger.info(`💰 تحديث الرصيد من WebSocket: $${this.currentBalance.toFixed(2)} (${this.isDemo ? 'تجريبي' : 'حقيقي'})`);

                return true;
            }
            return false;
        } catch (error) {
            this.logger.error('خطأ في تحديث الرصيد من WebSocket:', error);
            return false;
        }
    }

    /**
     * حفظ البيانات
     */
    async saveData() {
        try {
            const balanceFile = path.join(this.dataPath, 'balance.json');
            const data = {
                currentBalance: this.currentBalance,
                history: this.balanceHistory,
                lastUpdate: new Date().toISOString()
            };
            
            await fs.writeFile(balanceFile, JSON.stringify(data, null, 2));
            
        } catch (error) {
            this.logger.error('فشل في حفظ بيانات الرصيد:', error);
        }
    }

    /**
     * الحصول على حالة مدير الرصيد
     */
    getStatus() {
        return {
            currentBalance: this.currentBalance,
            isMonitoring: this.isMonitoring,
            historyCount: this.balanceHistory.length,
            lastUpdate: this.balanceHistory.length > 0 ? 
                this.balanceHistory[this.balanceHistory.length - 1].timestamp : null
        };
    }

    /**
     * الحصول على إحصائيات الرصيد
     */
    getStatistics() {
        if (this.balanceHistory.length === 0) {
            return null;
        }

        const balances = this.balanceHistory.map(h => h.balance);
        const changes = this.balanceHistory.map(h => h.change).filter(c => c !== 0);

        return {
            current: this.currentBalance,
            highest: Math.max(...balances),
            lowest: Math.min(...balances),
            totalChange: this.currentBalance - balances[0],
            averageChange: changes.length > 0 ? changes.reduce((a, b) => a + b, 0) / changes.length : 0,
            positiveChanges: changes.filter(c => c > 0).length,
            negativeChanges: changes.filter(c => c < 0).length
        };
    }
}

module.exports = { BalanceManager };
