/**
 * مدير البيانات الرئيسي
 */

const { EventEmitter } = require('events');
const { BalanceManager } = require('./BalanceManager');
const { InstrumentsManager } = require('./InstrumentsManager');
const { HistoricalDataManager } = require('./HistoricalDataManager');
const { LiveDataManager } = require('./LiveDataManager');

class DataManager extends EventEmitter {
    constructor(config, logger) {
        super();
        this.config = config;
        this.logger = logger;
        this.connection = null;
        this.isInitialized = false;
        
        // إنشاء مديري البيانات المختلفة
        this.balanceManager = new BalanceManager(config, logger);
        this.instrumentsManager = new InstrumentsManager(config, logger);
        this.historicalDataManager = new HistoricalDataManager(config, logger);
        this.liveDataManager = new LiveDataManager(config, logger);
        
        // ربط الأحداث
        this.setupEventHandlers();
    }

    /**
     * إعداد معالجات الأحداث
     */
    setupEventHandlers() {
        // أحداث الرصيد
        this.balanceManager.on('balanceUpdate', (balance) => {
            this.emit('balanceUpdate', balance);
        });

        this.balanceManager.on('error', (error) => {
            this.logger.error('خطأ في مدير الرصيد:', error);
        });

        // أحداث الأدوات المالية
        this.instrumentsManager.on('instrumentsUpdate', (instruments) => {
            this.emit('instrumentsUpdate', instruments);
        });

        this.instrumentsManager.on('error', (error) => {
            this.logger.error('خطأ في مدير الأدوات المالية:', error);
        });

        // أحداث البيانات التاريخية
        this.historicalDataManager.on('dataUpdate', (data) => {
            this.emit('historicalDataUpdate', data);
        });

        this.historicalDataManager.on('error', (error) => {
            this.logger.error('خطأ في مدير البيانات التاريخية:', error);
        });

        // أحداث البيانات المباشرة
        this.liveDataManager.on('candleUpdate', (candle) => {
            this.emit('candleUpdate', candle);
        });

        this.liveDataManager.on('priceUpdate', (price) => {
            this.emit('priceUpdate', price);
        });

        this.liveDataManager.on('error', (error) => {
            this.logger.error('خطأ في مدير البيانات المباشرة:', error);
        });
    }

    /**
     * تهيئة مدير البيانات
     */
    async initialize(connection) {
        try {
            this.logger.info('🔧 تهيئة مدير البيانات...');
            
            this.connection = connection;
            
            // تهيئة جميع المديرين
            await this.balanceManager.initialize(connection);
            await this.instrumentsManager.initialize(connection);
            await this.historicalDataManager.initialize(connection);
            await this.liveDataManager.initialize(connection);

            // ربط الأحداث بين المديرين
            this.setupEventListeners();

            this.isInitialized = true;
            this.logger.info('✅ تم تهيئة مدير البيانات بنجاح');
            
        } catch (error) {
            this.logger.error('❌ فشل في تهيئة مدير البيانات:', error);
            throw error;
        }
    }

    /**
     * إعداد مستمعي الأحداث بين المديرين
     */
    setupEventListeners() {
        // ربط البيانات التاريخية الواردة من LiveDataManager مع HistoricalDataManager
        this.liveDataManager.on('historicalDataReceived', (data) => {
            this.historicalDataManager.updateHistoricalDataFromWebSocket(data.symbol, data.candles);
        });

        // ربط تحديثات الرصيد
        this.liveDataManager.on('balanceUpdate', (balanceData) => {
            this.balanceManager.updateBalance(balanceData);
        });

        // ربط تحديثات الأدوات المالية
        this.liveDataManager.on('instrumentsUpdate', (instrumentsData) => {
            this.instrumentsManager.updateInstruments(instrumentsData.instruments);
        });

        // ربط تحديثات الصفقات
        this.liveDataManager.on('tradesUpdate', (tradesData) => {
            this.emit('tradesUpdate', tradesData);
        });

        // ربط نتائج الصفقات
        this.liveDataManager.on('tradeResult', (tradeResult) => {
            this.emit('tradeResult', tradeResult);
        });

        // إعادة توجيه الأحداث المهمة
        this.liveDataManager.on('candlesUpdate', (data) => {
            this.emit('candlesUpdate', data);
        });

        this.historicalDataManager.on('dataUpdate', (data) => {
            this.emit('historicalDataUpdate', data);
        });

        this.balanceManager.on('balanceUpdate', (data) => {
            this.emit('balanceUpdate', data);
        });

        this.instrumentsManager.on('instrumentsUpdate', (data) => {
            this.emit('instrumentsUpdate', data);
        });
    }

    /**
     * بدء جمع البيانات
     */
    async startDataCollection() {
        if (!this.isInitialized) {
            throw new Error('يجب تهيئة مدير البيانات أولاً');
        }

        try {
            this.logger.info('📊 بدء جمع البيانات...');
            
            // جلب البيانات الأساسية
            await this.loadInitialData();
            
            // بدء مراقبة البيانات المباشرة
            await this.liveDataManager.startMonitoring();
            
            // بدء مراقبة الرصيد
            await this.balanceManager.startMonitoring();
            
            this.logger.info('✅ تم بدء جمع البيانات بنجاح');
            
        } catch (error) {
            this.logger.error('❌ فشل في بدء جمع البيانات:', error);
            throw error;
        }
    }

    /**
     * تحميل البيانات الأولية
     */
    async loadInitialData() {
        try {
            this.logger.info('📥 تحميل البيانات الأولية...');
            
            // جلب الرصيد الحالي
            const balance = await this.balanceManager.getCurrentBalance();
            this.logger.info(`💰 الرصيد الحالي: $${balance}`);
            
            // جلب قائمة الأدوات المالية
            const instruments = await this.instrumentsManager.getInstruments();
            this.logger.info(`📋 تم جلب ${instruments.length} أداة مالية`);
            
            // جلب البيانات التاريخية للأدوات الرئيسية
            const mainInstruments = instruments.filter(inst => inst.isActive).slice(0, 10);
            for (const instrument of mainInstruments) {
                await this.historicalDataManager.loadHistoricalData(instrument.symbol);
            }
            
            this.logger.info('✅ تم تحميل البيانات الأولية بنجاح');
            
        } catch (error) {
            this.logger.error('❌ فشل في تحميل البيانات الأولية:', error);
            throw error;
        }
    }

    /**
     * إيقاف جمع البيانات
     */
    async stop() {
        try {
            this.logger.info('📴 إيقاف جمع البيانات...');
            
            await this.liveDataManager.stopMonitoring();
            await this.balanceManager.stopMonitoring();
            
            this.logger.info('✅ تم إيقاف جمع البيانات بنجاح');
            
        } catch (error) {
            this.logger.error('❌ خطأ في إيقاف جمع البيانات:', error);
        }
    }

    /**
     * الحصول على الرصيد الحالي
     */
    async getBalance() {
        return await this.balanceManager.getCurrentBalance();
    }

    /**
     * الحصول على قائمة الأدوات المالية
     */
    async getInstruments() {
        return await this.instrumentsManager.getInstruments();
    }

    /**
     * الحصول على البيانات التاريخية
     */
    async getHistoricalData(symbol, timeframe = '5m', count = 500) {
        return await this.historicalDataManager.getHistoricalData(symbol, timeframe, count);
    }

    /**
     * الحصول على آخر شمعة
     */
    async getLatestCandle(symbol) {
        return await this.liveDataManager.getLatestCandle(symbol);
    }

    /**
     * الحصول على السعر الحالي
     */
    async getCurrentPrice(symbol) {
        return await this.liveDataManager.getCurrentPrice(symbol);
    }

    /**
     * الحصول على الرصيد الحالي (مخزن محلياً)
     */
    getCurrentBalance() {
        return this.balanceManager.getStoredBalance();
    }

    /**
     * الحصول على حالة مدير البيانات
     */
    getStatus() {
        return {
            initialized: this.isInitialized,
            balance: this.balanceManager.getStatus(),
            instruments: this.instrumentsManager.getStatus(),
            historicalData: this.historicalDataManager.getStatus(),
            liveData: this.liveDataManager.getStatus()
        };
    }

    /**
     * حفظ البيانات في ملفات
     */
    async saveData() {
        try {
            await this.balanceManager.saveData();
            await this.instrumentsManager.saveData();
            await this.historicalDataManager.saveData();
            await this.liveDataManager.saveData();
            
            this.logger.info('✅ تم حفظ جميع البيانات');
            
        } catch (error) {
            this.logger.error('❌ فشل في حفظ البيانات:', error);
        }
    }

    /**
     * تحديث البيانات يدوياً
     */
    async refreshData() {
        try {
            this.logger.info('🔄 تحديث البيانات...');
            
            await this.balanceManager.refresh();
            await this.instrumentsManager.refresh();
            
            this.logger.info('✅ تم تحديث البيانات بنجاح');
            
        } catch (error) {
            this.logger.error('❌ فشل في تحديث البيانات:', error);
        }
    }
}

module.exports = { DataManager };
