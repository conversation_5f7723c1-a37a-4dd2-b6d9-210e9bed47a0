
# 📡 WebSocket أوامر منصة التداول

ملف يحتوي على جميع أوامر WebSocket المكتشفة مع شرحها، كما وردت في تحليل ملف `ws_messages_final.json`.

---

## ✅ قائمة الأوامر الكاملة

| الأمر | جهة الإرسال | الوظيفة | مثال |
|-------|--------------|---------|-------|
| `authorization` | إرسال | تسجيل الدخول | `42["authorization", {"session": "XYZ", "isDemo": 0}]` |
| `instruments/list` | استقبال | جلب قائمة الأزواج | يتم تلقائيًا بعد التفويض |
| `instruments/update` | إرسال | تحديث إعدادات الزوج | `42["instruments/update", {"asset": "USDTRY_otc", "period": 300}]` |
| `settings/store` | إرسال | حفظ إعدادات التداول | يتضمن الأصل، المبلغ، المدة، النوع |
| `orders/open` | إرسال | فتح صفقة جديدة | `42["orders/open", {...}]` |
| `orders/opened/list` | استقبال | استعراض الصفقات المفتوحة | تلقائيًا أو عند الطلب |
| `orders/closed/list` | استقبال | استعراض الصفقات المغلقة | تلقائيًا أو عند الطلب |
| `pending/list` | إرسال | استعراض الأوامر المعلقة | `42["pending/list"]` |
| `s_balance` | استقبال | الرصيد الحالي | تلقائيًا |
| `balance/list` | إرسال | جلب الرصيد الكلي | `42["balance/list"]` |
| `chart/load` | إرسال | جلب الشموع التاريخية | `42["chart/load", {"pair": "BTCUSD", "period": 60}]` |
| `chart/subscribe` | إرسال | بث الشموع الحية | `42["chart/subscribe", {"pair": "BTCUSD", "period": 60}]` |
| `trades/subscribe` | إرسال | بث الصفقات الحية | `42["trades/subscribe", {"pair": "BTCUSD"}]` |
| `quotes/stream` | استقبال | تدفق الأسعار | بيانات مشفرة |
| `depth/follow` | إرسال | متابعة عمق السوق | `42["depth/follow", "USDTRY_otc"]` |
| `depth/unfollow` | إرسال | إلغاء متابعة السوق | `42["depth/unfollow", "USDTRY_otc"]` |
| `chart_notification/get` | إرسال | تنبيهات الشارت | `42["chart_notification/get", {"asset": "..."}]` |
| `indicator/list` | إرسال | مؤشرات التحليل | `42["indicator/list"]` |
| `drawing/load` | إرسال | تحميل الرسومات | `42["drawing/load"]` |
| `history/list/v2` | استقبال | السجل الزمني | تلقائيًا |
| `tick` | إرسال | تحديث للسوق | `42["tick"]` |
| `signal/change` | استقبال | تغيير الإشارة | مشفر |
| `s_authorization` | استقبال | تأكيد التفويض | تلقائيًا بعد الدخول |

---

## 🛠️ مثال مفصل: فتح صفقة

```json
42["orders/open", {
  "asset": "USDTRY_otc",
  "amount": 1,
  "time": 30,
  "action": "put",
  "isDemo": 1,
  "tournamentId": 0,
  "requestId": 1751414228,
  "optionType": 100
}]
```

---

## ℹ️ ملاحظات:

- `action`: يمكن أن تكون `"call"` للشراء أو `"put"` للبيع.
- `isDemo`: 1 = حساب تجريبي، 0 = حقيقي.
- `optionType`: عادة 100 تعني خيار رقمي.

---

تم استخراج جميع الأوامر والتحقق من عدم وجود إضافات أخرى في الملف.
