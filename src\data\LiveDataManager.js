/**
 * مدير البيانات الحية
 */

const { EventEmitter } = require('events');
const fs = require('fs').promises;
const path = require('path');

class LiveDataManager extends EventEmitter {
    constructor(config, logger) {
        super();
        this.config = config;
        this.logger = logger;
        this.connection = null;
        this.isMonitoring = false;
        this.currentPrices = new Map(); // symbol -> price data
        this.currentCandles = new Map(); // symbol -> current candle
        this.priceSubscriptions = new Set(); // subscribed symbols
        this.updateInterval = config.get('data.updateInterval') || 1000;
        this.timeframe = config.get('data.timeframe') || '5m';
        this.dataPath = config.get('data.dataPath') || './data';
        this.monitoringInterval = null;
        this.wsMessageHandler = null;
    }

    /**
     * تهيئة مدير البيانات الحية
     */
    async initialize(connection) {
        try {
            this.connection = connection;
            
            // إنشاء مجلد البيانات
            await this.ensureDataDirectory();
            
            // إعداد معالج رسائل WebSocket
            this.setupWebSocketHandler();
            
            this.logger.info('✅ تم تهيئة مدير البيانات الحية بنجاح');
            
        } catch (error) {
            this.logger.error('❌ فشل في تهيئة مدير البيانات الحية:', error);
            throw error;
        }
    }

    /**
     * التأكد من وجود مجلد البيانات
     */
    async ensureDataDirectory() {
        try {
            const liveDir = path.join(this.dataPath, 'live');
            await fs.mkdir(liveDir, { recursive: true });
        } catch (error) {
            // تجاهل الخطأ إذا كان المجلد موجوداً
        }
    }

    /**
     * إعداد معالج رسائل WebSocket
     */
    setupWebSocketHandler() {
        if (!this.connection) return;

        this.wsMessageHandler = (data) => {
            this.processWebSocketMessage(data);
        };

        // الاستماع لرسائل WebSocket
        this.connection.on('websocket_message', this.wsMessageHandler);
    }

    /**
     * معالجة رسائل WebSocket
     */
    processWebSocketMessage(data) {
        try {
            if (data.type === 'received') {
                const message = data.message;

                this.logger.debug('📨 رسالة WebSocket واردة:', message.substring(0, 100));

                // تحليل رسائل Socket.IO
                this.parseSocketIOMessage(message);

                // تحليل رسائل تحديث الأسعار
                if (message.includes('quotes') || message.includes('price')) {
                    this.processPriceUpdate(message);
                }

                // تحليل رسائل الشموع
                if (message.includes('candle') || message.includes('ohlc')) {
                    this.processCandleUpdate(message);
                }

                // تحليل رسائل الأدوات المالية
                if (message.includes('instruments')) {
                    this.processInstrumentsMessage(message);
                }

                // تحليل البيانات المشفرة
                if (message.startsWith('451-') || message.startsWith('42[')) {
                    this.processEncodedMessage(message);
                }
            }
        } catch (error) {
            this.logger.debug('خطأ في معالجة رسالة WebSocket:', error);
        }
    }

    /**
     * تحليل رسائل Socket.IO
     */
    parseSocketIOMessage(message) {
        try {
            // رسائل الاتصال (نوع 0)
            if (message.startsWith('0{')) {
                const connectionData = JSON.parse(message.substring(1));
                this.logger.connection('🔗 بيانات الاتصال:', {
                    sessionId: connectionData.sid,
                    pingInterval: connectionData.pingInterval
                });
                return;
            }

            // رسائل التفويض (نوع 42)
            if (message.startsWith('42[')) {
                const eventMatch = message.match(/42\[(.*)\]/);
                if (eventMatch) {
                    const eventData = JSON.parse(`[${eventMatch[1]}]`);
                    if (eventData[0] === 's_authorization') {
                        this.logger.connection('✅ تم تأكيد التفويض من الخادم');
                    }
                }
                return;
            }

            // رسائل البيانات المشفرة (نوع 451)
            if (message.startsWith('451-')) {
                this.processEncodedMessage(message);
                return;
            }

        } catch (error) {
            this.logger.debug('خطأ في تحليل رسالة Socket.IO:', error);
        }
    }

    /**
     * معالجة تحديث الأسعار
     */
    processPriceUpdate(message) {
        try {
            // محاولة استخراج بيانات الأسعار من الرسالة
            const priceData = this.extractPriceData(message);
            
            if (priceData) {
                for (const price of priceData) {
                    this.updatePrice(price.symbol, price);
                }
            }
        } catch (error) {
            this.logger.debug('خطأ في معالجة تحديث الأسعار:', error);
        }
    }

    /**
     * معالجة تحديث الشموع
     */
    processCandleUpdate(message) {
        try {
            // محاولة استخراج بيانات الشموع من الرسالة
            const candleData = this.extractCandleData(message);

            if (candleData) {
                for (const candle of candleData) {
                    this.updateCandle(candle.symbol, candle);
                }
            }
        } catch (error) {
            this.logger.debug('خطأ في معالجة تحديث الشموع:', error);
        }
    }

    /**
     * معالجة رسائل الأدوات المالية
     */
    processInstrumentsMessage(message) {
        try {
            this.logger.data('📊 رسالة أدوات مالية:', message.substring(0, 200));

            // محاولة فك تشفير البيانات
            const decodedData = this.decodeBase64Message(message);
            if (decodedData) {
                this.logger.data('📊 بيانات أدوات مالية مفكوكة التشفير:', decodedData.substring(0, 500));

                // محاولة تحليل JSON
                try {
                    const instrumentsData = JSON.parse(decodedData);
                    if (Array.isArray(instrumentsData)) {
                        this.logger.data(`📊 تم العثور على ${instrumentsData.length} أداة مالية`);
                        this.emit('instrumentsUpdate', instrumentsData);
                    }
                } catch (jsonError) {
                    this.logger.debug('البيانات ليست JSON صالح');
                }
            }
        } catch (error) {
            this.logger.debug('خطأ في معالجة رسالة الأدوات المالية:', error);
        }
    }

    /**
     * معالجة البيانات المشفرة
     */
    processEncodedMessage(message) {
        try {
            // فك تشفير الرسالة
            const decodedData = this.decodeBase64Message(message);
            if (decodedData) {
                this.logger.data('🔓 بيانات مفكوكة التشفير:', decodedData.substring(0, 300));

                // محاولة تحليل البيانات كـ JSON
                try {
                    const jsonData = JSON.parse(decodedData);

                    // فحص إذا كانت بيانات أسعار
                    if (Array.isArray(jsonData) && jsonData.length > 0) {
                        if (typeof jsonData[0] === 'object' && jsonData[0].hasOwnProperty('symbol')) {
                            // بيانات أسعار
                            this.processPriceData(jsonData);
                        } else if (Array.isArray(jsonData[0])) {
                            // بيانات مصفوفة (ربما أسعار أو شموع)
                            this.processArrayData(jsonData);
                        }
                    }
                } catch (jsonError) {
                    // ليست بيانات JSON، ربما نص عادي
                    this.logger.debug('البيانات المفكوكة ليست JSON صالح');
                }
            }
        } catch (error) {
            this.logger.debug('خطأ في معالجة البيانات المشفرة:', error);
        }
    }

    /**
     * فك تشفير رسالة Base64
     */
    decodeBase64Message(message) {
        try {
            // استخراج الجزء المشفر من الرسالة
            let encodedPart = message;

            // إزالة بادئات Socket.IO
            if (message.startsWith('451-')) {
                encodedPart = message.substring(4);
            }

            // إزالة الأقواس المربعة إذا وجدت
            if (encodedPart.startsWith('[') && encodedPart.endsWith(']')) {
                const match = encodedPart.match(/\["[^"]*",\{"_placeholder":true,"num":(\d+)\}\]/);
                if (match) {
                    // هذه رسالة placeholder، لا توجد بيانات مشفرة
                    return null;
                }
            }

            // محاولة فك تشفير Base64
            try {
                const decoded = Buffer.from(encodedPart, 'base64').toString('utf8');
                if (decoded && decoded.length > 0 && decoded !== encodedPart) {
                    return decoded;
                }
            } catch (base64Error) {
                // ليست بيانات Base64 صالحة
            }

            return null;
        } catch (error) {
            return null;
        }
    }

    /**
     * معالجة بيانات الأسعار
     */
    processPriceData(priceArray) {
        try {
            for (const priceItem of priceArray) {
                if (priceItem.symbol && priceItem.price) {
                    this.updatePrice(priceItem.symbol, {
                        price: priceItem.price,
                        change: priceItem.change || 0,
                        timestamp: new Date().toISOString()
                    });
                }
            }
        } catch (error) {
            this.logger.debug('خطأ في معالجة بيانات الأسعار:', error);
        }
    }

    /**
     * معالجة البيانات المصفوفة
     */
    processArrayData(arrayData) {
        try {
            for (const item of arrayData) {
                if (Array.isArray(item) && item.length >= 4) {
                    // ربما بيانات سعر: [symbol, price, change, timestamp]
                    const symbol = item[0];
                    const price = item[1];

                    if (typeof symbol === 'string' && typeof price === 'number') {
                        this.updatePrice(symbol, {
                            price: price,
                            change: item[2] || 0,
                            timestamp: item[3] || new Date().toISOString()
                        });
                    }
                }
            }
        } catch (error) {
            this.logger.debug('خطأ في معالجة البيانات المصفوفة:', error);
        }
    }

    /**
     * استخراج بيانات الأسعار من الرسالة
     */
    extractPriceData(message) {
        try {
            // محاولة تحليل الرسالة كـ JSON
            if (message.startsWith('[') || message.startsWith('{')) {
                const data = JSON.parse(message);
                
                if (Array.isArray(data)) {
                    return data.map(item => this.formatPriceItem(item)).filter(Boolean);
                } else if (data.symbol && data.price) {
                    return [this.formatPriceItem(data)];
                }
            }
            
            // محاولة استخراج الأسعار من النص
            const priceMatches = message.match(/([A-Z]{3}[A-Z]{3}|[A-Z]{3}USD).*?(\d+\.?\d*)/g);
            if (priceMatches) {
                return priceMatches.map(match => {
                    const symbolMatch = match.match(/([A-Z]{3}[A-Z]{3}|[A-Z]{3}USD)/);
                    const priceMatch = match.match(/(\d+\.?\d*)/);
                    
                    if (symbolMatch && priceMatch) {
                        return {
                            symbol: symbolMatch[1],
                            price: parseFloat(priceMatch[1]),
                            timestamp: new Date().toISOString()
                        };
                    }
                    return null;
                }).filter(Boolean);
            }
            
            return null;
        } catch (error) {
            return null;
        }
    }

    /**
     * استخراج بيانات الشموع من الرسالة
     */
    extractCandleData(message) {
        try {
            // محاولة تحليل الرسالة كـ JSON
            if (message.startsWith('[') || message.startsWith('{')) {
                const data = JSON.parse(message);
                
                if (Array.isArray(data)) {
                    return data.map(item => this.formatCandleItem(item)).filter(Boolean);
                } else if (data.symbol && (data.open || data.close)) {
                    return [this.formatCandleItem(data)];
                }
            }
            
            return null;
        } catch (error) {
            return null;
        }
    }

    /**
     * تنسيق عنصر السعر
     */
    formatPriceItem(item) {
        if (Array.isArray(item) && item.length >= 2) {
            return {
                symbol: item[0],
                price: parseFloat(item[1]),
                timestamp: new Date().toISOString(),
                change: item[2] ? parseFloat(item[2]) : 0
            };
        } else if (typeof item === 'object' && item.symbol) {
            return {
                symbol: item.symbol,
                price: parseFloat(item.price || item.value || item.last),
                timestamp: new Date().toISOString(),
                change: item.change ? parseFloat(item.change) : 0
            };
        }
        return null;
    }

    /**
     * تنسيق عنصر الشمعة
     */
    formatCandleItem(item) {
        if (Array.isArray(item) && item.length >= 6) {
            return {
                symbol: item[0],
                timestamp: new Date(item[1]).toISOString(),
                open: parseFloat(item[2]),
                high: parseFloat(item[3]),
                low: parseFloat(item[4]),
                close: parseFloat(item[5]),
                volume: item[6] ? parseFloat(item[6]) : 0
            };
        } else if (typeof item === 'object' && item.symbol) {
            return {
                symbol: item.symbol,
                timestamp: item.timestamp || new Date().toISOString(),
                open: parseFloat(item.open),
                high: parseFloat(item.high),
                low: parseFloat(item.low),
                close: parseFloat(item.close),
                volume: item.volume ? parseFloat(item.volume) : 0
            };
        }
        return null;
    }

    /**
     * تحديث السعر
     */
    updatePrice(symbol, priceData) {
        const previousPrice = this.currentPrices.get(symbol);
        this.currentPrices.set(symbol, priceData);
        
        // إرسال حدث تحديث السعر
        this.emit('priceUpdate', {
            symbol,
            price: priceData.price,
            change: priceData.change,
            timestamp: priceData.timestamp,
            previousPrice: previousPrice ? previousPrice.price : null
        });
        
        this.logger.data(`💱 تحديث سعر ${symbol}: ${priceData.price}`);
    }

    /**
     * تحديث الشمعة
     */
    updateCandle(symbol, candleData) {
        const previousCandle = this.currentCandles.get(symbol);
        this.currentCandles.set(symbol, candleData);
        
        // إرسال حدث تحديث الشمعة
        this.emit('candleUpdate', {
            symbol,
            candle: candleData,
            isNew: !previousCandle || previousCandle.timestamp !== candleData.timestamp
        });
        
        this.logger.data(`🕯️ تحديث شمعة ${symbol}: O:${candleData.open} H:${candleData.high} L:${candleData.low} C:${candleData.close}`);
    }

    /**
     * بدء المراقبة
     */
    async startMonitoring() {
        if (this.isMonitoring) {
            this.logger.warn('⚠️ مراقبة البيانات الحية نشطة بالفعل');
            return;
        }

        try {
            this.logger.info('👁️ بدء مراقبة البيانات الحية...');
            
            // بدء المراقبة الدورية
            this.monitoringInterval = setInterval(async () => {
                try {
                    await this.pollLiveData();
                } catch (error) {
                    this.logger.error('خطأ في استطلاع البيانات الحية:', error);
                }
            }, this.updateInterval);
            
            this.isMonitoring = true;
            this.logger.info('✅ تم بدء مراقبة البيانات الحية بنجاح');
            
        } catch (error) {
            this.logger.error('❌ فشل في بدء مراقبة البيانات الحية:', error);
            throw error;
        }
    }

    /**
     * إيقاف المراقبة
     */
    async stopMonitoring() {
        if (!this.isMonitoring) {
            return;
        }

        try {
            if (this.monitoringInterval) {
                clearInterval(this.monitoringInterval);
                this.monitoringInterval = null;
            }
            
            // إزالة معالج رسائل WebSocket
            if (this.connection && this.wsMessageHandler) {
                this.connection.removeListener('websocket_message', this.wsMessageHandler);
            }
            
            this.isMonitoring = false;
            
            // حفظ البيانات النهائية
            await this.saveData();
            
            this.logger.info('✅ تم إيقاف مراقبة البيانات الحية');
            
        } catch (error) {
            this.logger.error('❌ خطأ في إيقاف مراقبة البيانات الحية:', error);
        }
    }

    /**
     * استطلاع البيانات الحية
     */
    async pollLiveData() {
        if (!this.connection || !this.connection.isConnected()) {
            return;
        }

        try {
            // جلب الأسعار الحالية من واجهة المستخدم
            const prices = await this.connection.evaluate(() => {
                const priceElements = document.querySelectorAll('[data-symbol], .price, .rate, .quote');
                const prices = [];
                
                priceElements.forEach(element => {
                    const symbol = element.getAttribute('data-symbol') || 
                                 element.className.match(/([A-Z]{3}[A-Z]{3})/)?.[1];
                    const priceText = element.textContent || element.innerText;
                    const priceMatch = priceText.match(/(\d+\.?\d*)/);
                    
                    if (symbol && priceMatch) {
                        prices.push({
                            symbol: symbol,
                            price: parseFloat(priceMatch[1]),
                            timestamp: new Date().toISOString()
                        });
                    }
                });
                
                return prices;
            });

            // معالجة الأسعار المجمعة
            if (prices && prices.length > 0) {
                prices.forEach(price => {
                    this.updatePrice(price.symbol, price);
                });
            }
            
        } catch (error) {
            this.logger.debug('خطأ في استطلاع البيانات الحية:', error);
        }
    }

    /**
     * الاشتراك في رمز معين
     */
    subscribeToSymbol(symbol) {
        this.priceSubscriptions.add(symbol);
        this.logger.info(`📡 الاشتراك في ${symbol}`);
    }

    /**
     * إلغاء الاشتراك في رمز معين
     */
    unsubscribeFromSymbol(symbol) {
        this.priceSubscriptions.delete(symbol);
        this.logger.info(`📡 إلغاء الاشتراك في ${symbol}`);
    }

    /**
     * الحصول على السعر الحالي
     */
    getCurrentPrice(symbol) {
        return this.currentPrices.get(symbol);
    }

    /**
     * الحصول على الشمعة الحالية
     */
    getLatestCandle(symbol) {
        return this.currentCandles.get(symbol);
    }

    /**
     * الحصول على جميع الأسعار الحالية
     */
    getAllCurrentPrices() {
        return Object.fromEntries(this.currentPrices);
    }

    /**
     * حفظ البيانات
     */
    async saveData() {
        try {
            const liveDir = path.join(this.dataPath, 'live');
            
            // حفظ الأسعار الحالية
            const pricesFile = path.join(liveDir, 'current_prices.json');
            const pricesData = {
                prices: Object.fromEntries(this.currentPrices),
                lastUpdate: new Date().toISOString()
            };
            await fs.writeFile(pricesFile, JSON.stringify(pricesData, null, 2));
            
            // حفظ الشموع الحالية
            const candlesFile = path.join(liveDir, 'current_candles.json');
            const candlesData = {
                candles: Object.fromEntries(this.currentCandles),
                lastUpdate: new Date().toISOString()
            };
            await fs.writeFile(candlesFile, JSON.stringify(candlesData, null, 2));
            
        } catch (error) {
            this.logger.error('فشل في حفظ البيانات الحية:', error);
        }
    }

    /**
     * الحصول على حالة مدير البيانات الحية
     */
    getStatus() {
        return {
            isMonitoring: this.isMonitoring,
            subscribedSymbols: Array.from(this.priceSubscriptions),
            currentPricesCount: this.currentPrices.size,
            currentCandlesCount: this.currentCandles.size,
            updateInterval: this.updateInterval
        };
    }
}

module.exports = { LiveDataManager };
