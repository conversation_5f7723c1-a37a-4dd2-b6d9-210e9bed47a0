/**
 * ملف التشغيل الرئيسي لروبوت التداول
 */

const { main } = require('./src/index');

console.log('🤖 مرحباً بك في روبوت التداول Quotex');
console.log('=====================================\n');

console.log('📋 معلومات النظام:');
console.log(`   Node.js: ${process.version}`);
console.log(`   المنصة: ${process.platform}`);
console.log(`   الذاكرة: ${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)} MB`);
console.log(`   المجلد: ${process.cwd()}\n`);

console.log('🚀 بدء تشغيل الروبوت...\n');

// تشغيل الروبوت
main().catch(error => {
    console.error('❌ فشل في تشغيل الروبوت:', error);
    process.exit(1);
});
