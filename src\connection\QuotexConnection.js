/**
 * إدارة الاتصال مع منصة Quotex
 */

const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const { EventEmitter } = require('events');
const fs = require('fs').promises;
const path = require('path');

// استخدام plugin التخفي
puppeteer.use(StealthPlugin());

class QuotexConnection extends EventEmitter {
    constructor(config, logger) {
        super();
        this.config = config;
        this.logger = logger;
        this.browser = null;
        this.page = null;
        this.connected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = config.get('connection.retryAttempts') || 3;
        this.reconnectDelay = config.get('connection.retryDelay') || 5000;
        this.wsMessages = [];
        this.sessionData = null;
    }

    /**
     * الاتصال بالمنصة
     */
    async connect() {
        try {
            this.logger.connection('بدء الاتصال بمنصة Quotex...');
            
            // إطلاق المتصفح
            await this.launchBrowser();
            
            // إنشاء صفحة جديدة
            await this.createPage();
            
            // إعداد مراقبة الشبكة
            await this.setupNetworkMonitoring();
            
            // الانتقال إلى المنصة
            await this.navigateToQuotex();
            
            // انتظار تحميل المنصة
            await this.waitForPlatformLoad();
            
            // استخراج بيانات الجلسة
            await this.extractSessionData();
            
            this.connected = true;
            this.reconnectAttempts = 0;
            this.emit('connected');
            
            this.logger.connection('تم الاتصال بمنصة Quotex بنجاح');
            
        } catch (error) {
            this.logger.error('فشل في الاتصال بمنصة Quotex:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * إطلاق المتصفح
     */
    async launchBrowser() {
        const browserOptions = {
            headless: false, // نافذة مرئية للمراقبة
            defaultViewport: null,
            args: [
                '--start-maximized',
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ]
        };

        // إضافة user agent مخصص
        const userAgent = this.config.get('security.userAgent');
        if (userAgent) {
            browserOptions.args.push(`--user-agent=${userAgent}`);
        }

        this.browser = await puppeteer.launch(browserOptions);
        this.logger.connection('تم إطلاق المتصفح بنجاح');
    }

    /**
     * إنشاء صفحة جديدة
     */
    async createPage() {
        this.page = await this.browser.newPage();
        
        // تعيين viewport
        const viewport = this.config.get('security.viewport');
        if (viewport) {
            await this.page.setViewport(viewport);
        }

        // تعيين user agent
        const userAgent = this.config.get('security.userAgent');
        if (userAgent) {
            await this.page.setUserAgent(userAgent);
        }

        // إعداد headers إضافية
        await this.page.setExtraHTTPHeaders({
            'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
        });

        this.logger.connection('تم إنشاء صفحة جديدة');
    }

    /**
     * إعداد مراقبة الشبكة
     */
    async setupNetworkMonitoring() {
        // تفعيل اعتراض الطلبات
        await this.page.setRequestInterception(true);

        // مراقبة الطلبات
        this.page.on('request', (request) => {
            const url = request.url();
            
            // تسجيل طلبات API المهمة
            if (url.includes('/api/') || url.includes('/ws/') || url.includes('socket.io')) {
                this.logger.data('طلب API:', { 
                    url, 
                    method: request.method(),
                    headers: request.headers() 
                });
            }
            
            request.continue();
        });

        // مراقبة الاستجابات
        this.page.on('response', async (response) => {
            const url = response.url();
            
            // تسجيل استجابات API المهمة
            if (url.includes('/api/') || url.includes('/ws/')) {
                try {
                    const text = await response.text();
                    this.logger.data('استجابة API:', { 
                        url, 
                        status: response.status(),
                        data: text.substring(0, 500) // أول 500 حرف
                    });
                } catch (error) {
                    // تجاهل أخطاء قراءة الاستجابة
                }
            }
        });

        // مراقبة رسائل WebSocket
        await this.setupWebSocketMonitoring();

        this.logger.connection('تم إعداد مراقبة الشبكة');
    }

    /**
     * الانتقال إلى منصة Quotex
     */
    async navigateToQuotex() {
        const url = this.config.get('connection.url');
        const timeout = this.config.get('connection.timeout') || 60000;

        this.logger.connection(`الانتقال إلى: ${url}`);
        
        await this.page.goto(url, {
            waitUntil: 'networkidle2',
            timeout: timeout
        });

        // انتظار إضافي للتأكد من التحميل
        await this.page.waitForTimeout(3000);
        
        this.logger.connection('تم الانتقال إلى المنصة');
    }

    /**
     * انتظار تحميل المنصة
     */
    async waitForPlatformLoad() {
        try {
            // انتظار عناصر المنصة الأساسية
            await this.page.waitForSelector('canvas, .chart, .trading-panel', {
                timeout: 30000
            });

            // انتظار تحميل JavaScript
            await this.page.waitForFunction(() => {
                return window.io || window.socket || window.WebSocket;
            }, { timeout: 30000 });

            this.logger.connection('تم تحميل المنصة بنجاح');
            
        } catch (error) {
            this.logger.warn('تحذير: قد لا تكون المنصة محملة بالكامل:', error.message);
        }
    }

    /**
     * استخراج بيانات الجلسة
     */
    async extractSessionData() {
        try {
            this.sessionData = await this.page.evaluate(() => {
                const data = {
                    url: window.location.href,
                    userAgent: navigator.userAgent,
                    timestamp: Date.now(),
                    cookies: document.cookie,
                    localStorage: {},
                    sessionStorage: {}
                };

                // استخراج localStorage
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    data.localStorage[key] = localStorage.getItem(key);
                }

                // استخراج sessionStorage
                for (let i = 0; i < sessionStorage.length; i++) {
                    const key = sessionStorage.key(i);
                    data.sessionStorage[key] = sessionStorage.getItem(key);
                }

                // البحث عن معرف الجلسة
                const sessionMatch = document.cookie.match(/session[^=]*=([^;]+)/);
                if (sessionMatch) {
                    data.sessionId = sessionMatch[1];
                }

                return data;
            });

            this.logger.connection('تم استخراج بيانات الجلسة', {
                sessionId: this.sessionData.sessionId ? 'موجود' : 'غير موجود'
            });

        } catch (error) {
            this.logger.warn('فشل في استخراج بيانات الجلسة:', error);
        }
    }

    /**
     * قطع الاتصال
     */
    async disconnect() {
        try {
            this.logger.connection('بدء قطع الاتصال...');
            
            this.connected = false;
            
            if (this.page) {
                await this.page.close();
                this.page = null;
            }
            
            if (this.browser) {
                await this.browser.close();
                this.browser = null;
            }
            
            this.emit('disconnected');
            this.logger.connection('تم قطع الاتصال بنجاح');
            
        } catch (error) {
            this.logger.error('خطأ أثناء قطع الاتصال:', error);
        }
    }

    /**
     * إعادة الاتصال
     */
    async reconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            this.logger.error('تم الوصول للحد الأقصى من محاولات إعادة الاتصال');
            return false;
        }

        this.reconnectAttempts++;
        this.logger.connection(`محاولة إعادة الاتصال ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);

        try {
            await this.disconnect();
            await new Promise(resolve => setTimeout(resolve, this.reconnectDelay));
            await this.connect();
            return true;
        } catch (error) {
            this.logger.error('فشل في إعادة الاتصال:', error);
            return false;
        }
    }

    /**
     * تنفيذ كود JavaScript في الصفحة
     */
    async evaluate(func, ...args) {
        if (!this.page) {
            throw new Error('لا يوجد اتصال نشط');
        }
        return await this.page.evaluate(func, ...args);
    }

    /**
     * النقر على عنصر
     */
    async click(selector) {
        if (!this.page) {
            throw new Error('لا يوجد اتصال نشط');
        }
        await this.page.click(selector);
    }

    /**
     * كتابة نص في حقل
     */
    async type(selector, text) {
        if (!this.page) {
            throw new Error('لا يوجد اتصال نشط');
        }
        await this.page.type(selector, text);
    }

    /**
     * انتظار عنصر
     */
    async waitForSelector(selector, options = {}) {
        if (!this.page) {
            throw new Error('لا يوجد اتصال نشط');
        }
        return await this.page.waitForSelector(selector, options);
    }

    /**
     * أخذ لقطة شاشة
     */
    async screenshot(options = {}) {
        if (!this.page) {
            throw new Error('لا يوجد اتصال نشط');
        }
        return await this.page.screenshot(options);
    }

    /**
     * الحصول على حالة الاتصال
     */
    isConnected() {
        return this.connected && this.page && !this.page.isClosed();
    }

    /**
     * الحصول على معلومات الحالة
     */
    getStatus() {
        return {
            connected: this.isConnected(),
            reconnectAttempts: this.reconnectAttempts,
            sessionData: this.sessionData ? 'موجود' : 'غير موجود',
            browserOpen: this.browser ? 'نعم' : 'لا',
            pageOpen: this.page && !this.page.isClosed() ? 'نعم' : 'لا'
        };
    }

    /**
     * إعداد مراقبة WebSocket
     */
    async setupWebSocketMonitoring() {
        // حقن كود JavaScript لمراقبة WebSocket
        await this.page.evaluateOnNewDocument(() => {
            // مراقبة WebSocket الأصلي
            const originalWebSocket = window.WebSocket;
            window.WebSocket = function(url, protocols) {
                const ws = new originalWebSocket(url, protocols);

                // مراقبة الرسائل الواردة
                ws.addEventListener('message', (event) => {
                    console.log('WS_RECEIVED:', event.data);
                    window.wsMessages = window.wsMessages || [];
                    window.wsMessages.push({
                        type: 'received',
                        message: event.data,
                        timestamp: new Date().toISOString()
                    });
                });

                // مراقبة الرسائل الصادرة
                const originalSend = ws.send;
                ws.send = function(data) {
                    console.log('WS_SENT:', data);
                    window.wsMessages = window.wsMessages || [];
                    window.wsMessages.push({
                        type: 'sent',
                        message: data,
                        timestamp: new Date().toISOString()
                    });
                    return originalSend.call(this, data);
                };

                return ws;
            };

            // مراقبة Socket.IO إذا كان متوفراً
            if (window.io) {
                const originalConnect = window.io.connect || window.io;
                window.io.connect = function(...args) {
                    const socket = originalConnect.apply(this, args);

                    // مراقبة جميع الأحداث
                    const originalOn = socket.on;
                    socket.on = function(event, callback) {
                        return originalOn.call(this, event, function(...args) {
                            console.log('SOCKET_EVENT:', event, args);
                            window.socketEvents = window.socketEvents || [];
                            window.socketEvents.push({
                                event,
                                data: args,
                                timestamp: new Date().toISOString()
                            });
                            return callback.apply(this, args);
                        });
                    };

                    const originalEmit = socket.emit;
                    socket.emit = function(event, ...args) {
                        console.log('SOCKET_EMIT:', event, args);
                        window.socketEvents = window.socketEvents || [];
                        window.socketEvents.push({
                            event: `emit_${event}`,
                            data: args,
                            timestamp: new Date().toISOString()
                        });
                        return originalEmit.apply(this, [event, ...args]);
                    };

                    return socket;
                };
            }
        });

        // مراقبة رسائل الكونسول
        this.page.on('console', (msg) => {
            const text = msg.text();
            if (text.startsWith('WS_RECEIVED:') || text.startsWith('WS_SENT:')) {
                const data = text.substring(text.indexOf(':') + 1).trim();
                this.wsMessages.push({
                    type: text.startsWith('WS_RECEIVED:') ? 'received' : 'sent',
                    message: data,
                    timestamp: new Date().toISOString()
                });
                this.emit('websocket_message', {
                    type: text.startsWith('WS_RECEIVED:') ? 'received' : 'sent',
                    message: data
                });
            } else if (text.startsWith('SOCKET_EVENT:') || text.startsWith('SOCKET_EMIT:')) {
                this.emit('socket_event', text);
            }
        });
    }

    /**
     * الحصول على رسائل WebSocket المسجلة
     */
    async getWebSocketMessages() {
        if (!this.page) return [];

        try {
            const messages = await this.page.evaluate(() => {
                return window.wsMessages || [];
            });
            return messages;
        } catch (error) {
            this.logger.warn('فشل في الحصول على رسائل WebSocket:', error);
            return this.wsMessages;
        }
    }

    /**
     * الحصول على أحداث Socket.IO المسجلة
     */
    async getSocketEvents() {
        if (!this.page) return [];

        try {
            const events = await this.page.evaluate(() => {
                return window.socketEvents || [];
            });
            return events;
        } catch (error) {
            this.logger.warn('فشل في الحصول على أحداث Socket.IO:', error);
            return [];
        }
    }

    /**
     * إرسال رسالة WebSocket
     */
    async sendWebSocketMessage(message) {
        if (!this.page) {
            throw new Error('لا يوجد اتصال نشط');
        }

        return await this.page.evaluate((msg) => {
            // البحث عن WebSocket نشط
            if (window.socket && window.socket.send) {
                window.socket.send(msg);
                return true;
            }

            // البحث عن Socket.IO
            if (window.io && window.io.socket) {
                window.io.socket.emit('message', msg);
                return true;
            }

            return false;
        }, message);
    }

    /**
     * حفظ رسائل WebSocket في ملف
     */
    async saveWebSocketMessages(filePath) {
        const messages = await this.getWebSocketMessages();
        if (messages.length > 0) {
            await fs.writeFile(filePath, JSON.stringify(messages, null, 2));
            this.logger.connection(`تم حفظ ${messages.length} رسالة WebSocket`);
        }
    }

    /**
     * حفظ بيانات الجلسة
     */
    async saveSessionData(filePath) {
        if (this.sessionData) {
            await fs.writeFile(filePath, JSON.stringify(this.sessionData, null, 2));
            this.logger.connection('تم حفظ بيانات الجلسة');
        }
    }
}

module.exports = { QuotexConnection };
