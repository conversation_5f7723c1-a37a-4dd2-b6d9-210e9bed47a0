/**
 * إدارة الاتصال مع منصة Quotex
 */

const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const { EventEmitter } = require('events');
const fs = require('fs').promises;
const path = require('path');

// استخدام plugin التخفي
puppeteer.use(StealthPlugin());

class QuotexConnection extends EventEmitter {
    constructor(config, logger) {
        super();
        this.config = config;
        this.logger = logger;
        this.browser = null;
        this.page = null;
        this.connected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = config.get('connection.retryAttempts') || 3;
        this.reconnectDelay = config.get('connection.retryDelay') || 5000;
        this.wsMessages = [];
        this.sessionData = null;
    }

    /**
     * الاتصال بالمنصة
     */
    async connect() {
        try {
            this.logger.connection('بدء الاتصال بمنصة Quotex...');
            
            // إطلاق المتصفح
            await this.launchBrowser();
            
            // إنشاء صفحة جديدة
            await this.createPage();
            
            // إعداد مراقبة الشبكة
            await this.setupNetworkMonitoring();
            
            // الانتقال إلى المنصة
            await this.navigateToQuotex();
            
            // انتظار تحميل المنصة
            await this.waitForPlatformLoad();
            
            // استخراج بيانات الجلسة
            await this.extractSessionData();
            
            this.connected = true;
            this.reconnectAttempts = 0;
            this.emit('connected');

            this.logger.connection('تم الاتصال بمنصة Quotex بنجاح');

            // بدء عملية جلب البيانات الحقيقية
            await this.startDataCollection();
            
        } catch (error) {
            this.logger.error('فشل في الاتصال بمنصة Quotex:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * إطلاق المتصفح
     */
    async launchBrowser() {
        const browserOptions = {
            headless: false, // نافذة مرئية للمراقبة
            defaultViewport: null,
            args: [
                '--start-maximized',
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ]
        };

        // إضافة user agent مخصص
        const userAgent = this.config.get('security.userAgent');
        if (userAgent) {
            browserOptions.args.push(`--user-agent=${userAgent}`);
        }

        this.browser = await puppeteer.launch(browserOptions);
        this.logger.connection('تم إطلاق المتصفح بنجاح');
    }

    /**
     * إنشاء صفحة جديدة
     */
    async createPage() {
        this.page = await this.browser.newPage();
        
        // تعيين viewport
        const viewport = this.config.get('security.viewport');
        if (viewport) {
            await this.page.setViewport(viewport);
        }

        // تعيين user agent
        const userAgent = this.config.get('security.userAgent');
        if (userAgent) {
            await this.page.setUserAgent(userAgent);
        }

        // إعداد headers إضافية
        await this.page.setExtraHTTPHeaders({
            'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
        });

        this.logger.connection('تم إنشاء صفحة جديدة');
    }

    /**
     * إعداد مراقبة الشبكة
     */
    async setupNetworkMonitoring() {
        // تفعيل اعتراض الطلبات
        await this.page.setRequestInterception(true);

        // مراقبة الطلبات
        this.page.on('request', (request) => {
            const url = request.url();
            
            // تسجيل طلبات API المهمة
            if (url.includes('/api/') || url.includes('/ws/') || url.includes('socket.io')) {
                this.logger.data('طلب API:', { 
                    url, 
                    method: request.method(),
                    headers: request.headers() 
                });
            }
            
            request.continue();
        });

        // مراقبة الاستجابات
        this.page.on('response', async (response) => {
            const url = response.url();
            
            // تسجيل استجابات API المهمة
            if (url.includes('/api/') || url.includes('/ws/')) {
                try {
                    const text = await response.text();
                    this.logger.data('استجابة API:', { 
                        url, 
                        status: response.status(),
                        data: text.substring(0, 500) // أول 500 حرف
                    });
                } catch (error) {
                    // تجاهل أخطاء قراءة الاستجابة
                }
            }
        });

        // مراقبة رسائل WebSocket
        await this.setupWebSocketMonitoring();

        this.logger.connection('تم إعداد مراقبة الشبكة');
    }

    /**
     * الانتقال إلى منصة Quotex
     */
    async navigateToQuotex() {
        const url = this.config.get('connection.url');
        const timeout = this.config.get('connection.timeout') || 60000;

        this.logger.connection(`الانتقال إلى: ${url}`);
        
        await this.page.goto(url, {
            waitUntil: 'networkidle2',
            timeout: timeout
        });

        // انتظار إضافي للتأكد من التحميل
        await this.page.waitForTimeout(3000);
        
        this.logger.connection('تم الانتقال إلى المنصة');
    }

    /**
     * انتظار تحميل المنصة
     */
    async waitForPlatformLoad() {
        try {
            // انتظار عناصر المنصة الأساسية
            await this.page.waitForSelector('canvas, .chart, .trading-panel', {
                timeout: 30000
            });

            // انتظار تحميل JavaScript
            await this.page.waitForFunction(() => {
                return window.io || window.socket || window.WebSocket;
            }, { timeout: 30000 });

            this.logger.connection('تم تحميل المنصة بنجاح');
            
        } catch (error) {
            this.logger.warn('تحذير: قد لا تكون المنصة محملة بالكامل:', error.message);
        }
    }

    /**
     * استخراج بيانات الجلسة
     */
    async extractSessionData() {
        try {
            this.sessionData = await this.page.evaluate(() => {
                const data = {
                    url: window.location.href,
                    userAgent: navigator.userAgent,
                    timestamp: Date.now(),
                    cookies: document.cookie,
                    localStorage: {},
                    sessionStorage: {}
                };

                // استخراج localStorage
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    data.localStorage[key] = localStorage.getItem(key);
                }

                // استخراج sessionStorage
                for (let i = 0; i < sessionStorage.length; i++) {
                    const key = sessionStorage.key(i);
                    data.sessionStorage[key] = sessionStorage.getItem(key);
                }

                // البحث عن معرف الجلسة
                const sessionMatch = document.cookie.match(/session[^=]*=([^;]+)/);
                if (sessionMatch) {
                    data.sessionId = sessionMatch[1];
                }

                return data;
            });

            this.logger.connection('تم استخراج بيانات الجلسة', {
                sessionId: this.sessionData.sessionId ? 'موجود' : 'غير موجود'
            });

        } catch (error) {
            this.logger.warn('فشل في استخراج بيانات الجلسة:', error);
        }
    }

    /**
     * قطع الاتصال
     */
    async disconnect() {
        try {
            this.logger.connection('بدء قطع الاتصال...');

            this.connected = false;

            // إيقاف الطلبات الدورية
            this.stopPeriodicDataRequests();

            if (this.page) {
                await this.page.close();
                this.page = null;
            }

            if (this.browser) {
                await this.browser.close();
                this.browser = null;
            }

            this.emit('disconnected');
            this.logger.connection('تم قطع الاتصال بنجاح');

        } catch (error) {
            this.logger.error('خطأ أثناء قطع الاتصال:', error);
        }
    }

    /**
     * إعادة الاتصال
     */
    async reconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            this.logger.error('تم الوصول للحد الأقصى من محاولات إعادة الاتصال');
            return false;
        }

        this.reconnectAttempts++;
        this.logger.connection(`محاولة إعادة الاتصال ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);

        try {
            await this.disconnect();
            await new Promise(resolve => setTimeout(resolve, this.reconnectDelay));
            await this.connect();
            return true;
        } catch (error) {
            this.logger.error('فشل في إعادة الاتصال:', error);
            return false;
        }
    }

    /**
     * تنفيذ كود JavaScript في الصفحة
     */
    async evaluate(func, ...args) {
        if (!this.page) {
            throw new Error('لا يوجد اتصال نشط');
        }
        return await this.page.evaluate(func, ...args);
    }

    /**
     * النقر على عنصر
     */
    async click(selector) {
        if (!this.page) {
            throw new Error('لا يوجد اتصال نشط');
        }
        await this.page.click(selector);
    }

    /**
     * كتابة نص في حقل
     */
    async type(selector, text) {
        if (!this.page) {
            throw new Error('لا يوجد اتصال نشط');
        }
        await this.page.type(selector, text);
    }

    /**
     * انتظار عنصر
     */
    async waitForSelector(selector, options = {}) {
        if (!this.page) {
            throw new Error('لا يوجد اتصال نشط');
        }
        return await this.page.waitForSelector(selector, options);
    }

    /**
     * أخذ لقطة شاشة
     */
    async screenshot(options = {}) {
        if (!this.page) {
            throw new Error('لا يوجد اتصال نشط');
        }
        return await this.page.screenshot(options);
    }

    /**
     * الحصول على حالة الاتصال
     */
    isConnected() {
        return this.connected && this.page && !this.page.isClosed();
    }

    /**
     * الحصول على معلومات الحالة
     */
    getStatus() {
        return {
            connected: this.isConnected(),
            reconnectAttempts: this.reconnectAttempts,
            sessionData: this.sessionData ? 'موجود' : 'غير موجود',
            browserOpen: this.browser ? 'نعم' : 'لا',
            pageOpen: this.page && !this.page.isClosed() ? 'نعم' : 'لا'
        };
    }

    /**
     * إعداد مراقبة WebSocket
     */
    async setupWebSocketMonitoring() {
        // حقن كود JavaScript لمراقبة WebSocket
        await this.page.evaluateOnNewDocument(() => {
            // مراقبة WebSocket الأصلي
            const originalWebSocket = window.WebSocket;
            window.WebSocket = function(url, protocols) {
                const ws = new originalWebSocket(url, protocols);

                // مراقبة الرسائل الواردة
                ws.addEventListener('message', (event) => {
                    console.log('WS_RECEIVED:', event.data);
                    window.wsMessages = window.wsMessages || [];
                    window.wsMessages.push({
                        type: 'received',
                        message: event.data,
                        timestamp: new Date().toISOString()
                    });
                });

                // مراقبة الرسائل الصادرة
                const originalSend = ws.send;
                ws.send = function(data) {
                    console.log('WS_SENT:', data);
                    window.wsMessages = window.wsMessages || [];
                    window.wsMessages.push({
                        type: 'sent',
                        message: data,
                        timestamp: new Date().toISOString()
                    });
                    return originalSend.call(this, data);
                };

                return ws;
            };

            // مراقبة Socket.IO إذا كان متوفراً
            if (window.io) {
                const originalConnect = window.io.connect || window.io;
                window.io.connect = function(...args) {
                    const socket = originalConnect.apply(this, args);

                    // مراقبة جميع الأحداث
                    const originalOn = socket.on;
                    socket.on = function(event, callback) {
                        return originalOn.call(this, event, function(...args) {
                            console.log('SOCKET_EVENT:', event, args);
                            window.socketEvents = window.socketEvents || [];
                            window.socketEvents.push({
                                event,
                                data: args,
                                timestamp: new Date().toISOString()
                            });
                            return callback.apply(this, args);
                        });
                    };

                    const originalEmit = socket.emit;
                    socket.emit = function(event, ...args) {
                        console.log('SOCKET_EMIT:', event, args);
                        window.socketEvents = window.socketEvents || [];
                        window.socketEvents.push({
                            event: `emit_${event}`,
                            data: args,
                            timestamp: new Date().toISOString()
                        });
                        return originalEmit.apply(this, [event, ...args]);
                    };

                    return socket;
                };
            }
        });

        // مراقبة رسائل الكونسول
        this.page.on('console', (msg) => {
            const text = msg.text();
            if (text.startsWith('WS_RECEIVED:') || text.startsWith('WS_SENT:')) {
                const data = text.substring(text.indexOf(':') + 1).trim();
                this.wsMessages.push({
                    type: text.startsWith('WS_RECEIVED:') ? 'received' : 'sent',
                    message: data,
                    timestamp: new Date().toISOString()
                });
                this.emit('websocket_message', {
                    type: text.startsWith('WS_RECEIVED:') ? 'received' : 'sent',
                    message: data
                });
            } else if (text.startsWith('SOCKET_EVENT:') || text.startsWith('SOCKET_EMIT:')) {
                this.emit('socket_event', text);
            }
        });
    }

    /**
     * الحصول على رسائل WebSocket المسجلة
     */
    async getWebSocketMessages() {
        if (!this.page) return [];

        try {
            const messages = await this.page.evaluate(() => {
                return window.wsMessages || [];
            });
            return messages;
        } catch (error) {
            this.logger.warn('فشل في الحصول على رسائل WebSocket:', error);
            return this.wsMessages;
        }
    }

    /**
     * الحصول على أحداث Socket.IO المسجلة
     */
    async getSocketEvents() {
        if (!this.page) return [];

        try {
            const events = await this.page.evaluate(() => {
                return window.socketEvents || [];
            });
            return events;
        } catch (error) {
            this.logger.warn('فشل في الحصول على أحداث Socket.IO:', error);
            return [];
        }
    }

    /**
     * إرسال رسالة WebSocket
     */
    async sendWebSocketMessage(message) {
        if (!this.page) {
            throw new Error('لا يوجد اتصال نشط');
        }

        return await this.page.evaluate((msg) => {
            // البحث عن WebSocket نشط
            if (window.socket && window.socket.send) {
                window.socket.send(msg);
                return true;
            }

            // البحث عن Socket.IO
            if (window.io && window.io.socket) {
                window.io.socket.emit('message', msg);
                return true;
            }

            return false;
        }, message);
    }

    /**
     * إرسال رسالة تفويض إلى منصة Quotex
     */
    async sendAuthorizationMessage() {
        if (!this.page) {
            throw new Error('لا يوجد اتصال نشط');
        }

        try {
            // انتظار حتى يتم تحميل Socket.IO مع مهلة زمنية أطول
            try {
                await this.page.waitForFunction(() => {
                    return window.io && window.io.socket;
                }, { timeout: 15000 });
            } catch (timeoutError) {
                this.logger.warn('انتهت مهلة انتظار Socket.IO، محاولة المتابعة...');
            }

            // استخراج session token من الصفحة
            const sessionData = await this.page.evaluate(() => {
                // البحث في localStorage
                let sessionToken = localStorage.getItem('session') ||
                                 localStorage.getItem('sessionId') ||
                                 localStorage.getItem('auth_token') ||
                                 localStorage.getItem('user_session');

                if (!sessionToken) {
                    // البحث في cookies
                    const cookieMatch = document.cookie.match(/session[^=]*=([^;]+)/);
                    if (cookieMatch) sessionToken = cookieMatch[1];
                }

                if (!sessionToken) {
                    // البحث في متغيرات النافذة
                    if (window.sessionId) sessionToken = window.sessionId;
                    if (window.authToken) sessionToken = window.authToken;
                    if (window.userSession) sessionToken = window.userSession;
                }

                // محاولة استخراج من الصفحة مباشرة
                if (!sessionToken) {
                    const scripts = document.querySelectorAll('script');
                    for (const script of scripts) {
                        const content = script.textContent || script.innerHTML;
                        const sessionMatch = content.match(/session["\']?\s*:\s*["\']([^"']+)["\']/) ||
                                           content.match(/sessionId["\']?\s*:\s*["\']([^"']+)["\']/) ||
                                           content.match(/auth_token["\']?\s*:\s*["\']([^"']+)["\']/);
                        if (sessionMatch) {
                            sessionToken = sessionMatch[1];
                            break;
                        }
                    }
                }

                return {
                    sessionToken,
                    hasSocket: !!(window.io && window.io.socket),
                    socketConnected: !!(window.io && window.io.socket && window.io.socket.connected)
                };
            });

            if (!sessionData.sessionToken) {
                this.logger.warn('لم يتم العثور على session token');
                // محاولة استخدام session افتراضي للاختبار
                sessionData.sessionToken = 'demo_session_' + Date.now();
            }

            if (!sessionData.hasSocket) {
                this.logger.warn('Socket.IO غير متوفر');
                return false;
            }

            // إرسال رسالة التفويض باستخدام Socket.IO
            const result = await this.page.evaluate((sessionToken) => {
                try {
                    // محاولة إرسال عبر Socket.IO
                    if (window.io && window.io.socket) {
                        window.io.socket.emit('authorization', {
                            session: sessionToken,
                            isDemo: 1,
                            tournamentId: 0
                        });
                        console.log('تم إرسال رسالة التفويض عبر Socket.IO:', sessionToken);
                        return 'socket_io';
                    }

                    // محاولة إرسال عبر WebSocket مباشرة
                    if (window.WebSocket && window.socket) {
                        const authMessage = `42["authorization",{"session":"${sessionToken}","isDemo":1,"tournamentId":0}]`;
                        window.socket.send(authMessage);
                        console.log('تم إرسال رسالة التفويض عبر WebSocket:', sessionToken);
                        return 'websocket';
                    }

                    // محاولة البحث عن أي socket متاح
                    const sockets = Object.keys(window).filter(key => key.includes('socket') || key.includes('io'));
                    if (sockets.length > 0) {
                        console.log('تم العثور على sockets:', sockets);
                        return 'found_sockets';
                    }

                    return false;
                } catch (error) {
                    console.error('خطأ في إرسال رسالة التفويض:', error);
                    return false;
                }
            }, sessionData.sessionToken);

            if (result) {
                this.logger.connection(`✅ تم إرسال رسالة التفويض بنجاح عبر ${result}`);
                return true;
            } else {
                this.logger.warn('❌ فشل في إرسال رسالة التفويض - لم يتم العثور على socket');
                return false;
            }

        } catch (error) {
            this.logger.error('خطأ في إرسال رسالة التفويض:', error);
            return false;
        }
    }

    /**
     * طلب قائمة الأدوات المالية
     */
    async requestInstrumentsList() {
        if (!this.page) {
            throw new Error('لا يوجد اتصال نشط');
        }

        try {
            const result = await this.page.evaluate(() => {
                try {
                    if (window.io && window.io.socket && window.io.socket.emit) {
                        // طلب قائمة الأدوات المالية بالتنسيق الصحيح
                        window.io.socket.emit('instruments', {
                            type: 'list'
                        });

                        // طلب إضافي للأسعار
                        window.io.socket.emit('quotes', {
                            action: 'subscribe'
                        });

                        console.log('تم إرسال طلب قائمة الأدوات المالية');
                        return true;
                    }

                    // محاولة بديلة
                    if (window.socket && window.socket.send) {
                        const message = '42["instruments",{"type":"list"}]';
                        window.socket.send(message);
                        console.log('تم إرسال طلب الأدوات عبر WebSocket');
                        return true;
                    }

                    return false;
                } catch (error) {
                    console.error('خطأ في طلب قائمة الأدوات:', error);
                    return false;
                }
            });

            if (result) {
                this.logger.connection('✅ تم إرسال طلب قائمة الأدوات المالية');
                return true;
            } else {
                this.logger.warn('❌ فشل في إرسال طلب قائمة الأدوات المالية');
                return false;
            }

        } catch (error) {
            this.logger.error('خطأ في طلب قائمة الأدوات المالية:', error);
            return false;
        }
    }

    /**
     * الاشتراك في تحديثات الأسعار
     */
    async subscribeToQuotes(symbols = []) {
        if (!this.page) {
            throw new Error('لا يوجد اتصال نشط');
        }

        try {
            const result = await this.page.evaluate((symbolsList) => {
                try {
                    if (window.io && window.io.socket && window.io.socket.emit) {
                        // الاشتراك في تحديثات الأسعار الحية
                        window.io.socket.emit('quotes', {
                            action: 'subscribe',
                            symbols: symbolsList.length > 0 ? symbolsList : ['EURUSD', 'GBPUSD', 'AUDUSD', 'USDCAD', 'USDCHF', 'BTCUSD', 'ETHUSD']
                        });

                        // طلب البيانات التاريخية للشموع
                        const commonSymbols = ['EURUSD', 'GBPUSD', 'AUDUSD', 'USDCAD', 'USDCHF', 'BTCUSD', 'ETHUSD'];
                        commonSymbols.forEach(symbol => {
                            window.io.socket.emit('candles', {
                                symbol: symbol,
                                period: 300, // 5 دقائق
                                count: 500
                            });
                        });

                        // طلب معلومات الرصيد
                        window.io.socket.emit('balance', {
                            action: 'get'
                        });

                        // طلب سجل الصفقات
                        window.io.socket.emit('trades', {
                            action: 'history'
                        });

                        console.log('تم إرسال طلبات الاشتراك في البيانات');
                        return true;
                    }

                    return false;
                } catch (error) {
                    console.error('خطأ في الاشتراك في تحديثات الأسعار:', error);
                    return false;
                }
            }, symbols);

            if (result) {
                this.logger.connection('✅ تم الاشتراك في تحديثات الأسعار');
                return true;
            } else {
                this.logger.warn('❌ فشل في الاشتراك في تحديثات الأسعار');
                return false;
            }

        } catch (error) {
            this.logger.error('خطأ في الاشتراك في تحديثات الأسعار:', error);
            return false;
        }
    }

    /**
     * طلب البيانات التاريخية لرمز معين
     */
    async requestHistoricalData(symbol, period = 300, count = 500) {
        if (!this.page) {
            throw new Error('لا يوجد اتصال نشط');
        }

        try {
            const result = await this.page.evaluate((symbol, period, count) => {
                try {
                    if (window.io && window.io.socket && window.io.socket.emit) {
                        // طلب البيانات التاريخية
                        window.io.socket.emit('history', {
                            symbol: symbol,
                            period: period, // بالثواني (300 = 5 دقائق)
                            count: count    // عدد الشموع
                        });

                        console.log(`تم طلب البيانات التاريخية لـ ${symbol}`);
                        return true;
                    }
                    return false;
                } catch (error) {
                    console.error('خطأ في طلب البيانات التاريخية:', error);
                    return false;
                }
            }, symbol, period, count);

            if (result) {
                this.logger.connection(`✅ تم طلب البيانات التاريخية لـ ${symbol}`);
                return true;
            } else {
                this.logger.warn(`❌ فشل في طلب البيانات التاريخية لـ ${symbol}`);
                return false;
            }

        } catch (error) {
            this.logger.error(`خطأ في طلب البيانات التاريخية لـ ${symbol}:`, error);
            return false;
        }
    }

    /**
     * طلب معلومات الرصيد
     */
    async requestBalance() {
        if (!this.page) {
            throw new Error('لا يوجد اتصال نشط');
        }

        try {
            const result = await this.page.evaluate(() => {
                try {
                    if (window.io && window.io.socket && window.io.socket.emit) {
                        // طلب معلومات الرصيد
                        window.io.socket.emit('balance', {
                            action: 'get'
                        });

                        // طلب معلومات الحساب
                        window.io.socket.emit('account', {
                            action: 'info'
                        });

                        console.log('تم طلب معلومات الرصيد');
                        return true;
                    }
                    return false;
                } catch (error) {
                    console.error('خطأ في طلب معلومات الرصيد:', error);
                    return false;
                }
            });

            if (result) {
                this.logger.connection('✅ تم طلب معلومات الرصيد');
                return true;
            } else {
                this.logger.warn('❌ فشل في طلب معلومات الرصيد');
                return false;
            }

        } catch (error) {
            this.logger.error('خطأ في طلب معلومات الرصيد:', error);
            return false;
        }
    }

    /**
     * إجراء صفقة
     */
    async placeTrade(symbol, amount, direction, duration) {
        if (!this.page) {
            throw new Error('لا يوجد اتصال نشط');
        }

        try {
            const result = await this.page.evaluate((symbol, amount, direction, duration) => {
                try {
                    if (window.io && window.io.socket && window.io.socket.emit) {
                        // إجراء صفقة
                        window.io.socket.emit('trade', {
                            symbol: symbol,
                            amount: amount,
                            direction: direction, // 'call' أو 'put'
                            duration: duration,   // مدة الصفقة بالثواني
                            timestamp: Date.now()
                        });

                        console.log(`تم إجراء صفقة ${direction} على ${symbol} بمبلغ ${amount}`);
                        return true;
                    }
                    return false;
                } catch (error) {
                    console.error('خطأ في إجراء الصفقة:', error);
                    return false;
                }
            }, symbol, amount, direction, duration);

            if (result) {
                this.logger.connection(`✅ تم إجراء صفقة ${direction} على ${symbol}`);
                return true;
            } else {
                this.logger.warn(`❌ فشل في إجراء صفقة على ${symbol}`);
                return false;
            }

        } catch (error) {
            this.logger.error(`خطأ في إجراء صفقة على ${symbol}:`, error);
            return false;
        }
    }

    /**
     * بدء عملية جلب البيانات الحقيقية من المنصة
     */
    async startDataCollection() {
        try {
            this.logger.connection('🚀 بدء عملية جلب البيانات الحقيقية...');

            // انتظار قليل للتأكد من تحميل Socket.IO
            await this.page.waitForTimeout(3000);

            // إرسال رسالة التفويض
            await this.sendAuthorizationMessage();
            await this.page.waitForTimeout(2000);

            // طلب معلومات الرصيد
            await this.requestBalance();
            await this.page.waitForTimeout(1000);

            // طلب قائمة الأدوات المالية
            await this.requestInstrumentsList();
            await this.page.waitForTimeout(1000);

            // الاشتراك في تحديثات الأسعار
            await this.subscribeToQuotes();
            await this.page.waitForTimeout(1000);

            // طلب البيانات التاريخية للرموز الشائعة
            const commonSymbols = ['EURUSD', 'GBPUSD', 'AUDUSD', 'USDCAD', 'USDCHF', 'BTCUSD', 'ETHUSD', 'LTCUSD', 'ADAUSD', 'DOGUSD'];
            for (const symbol of commonSymbols) {
                await this.requestHistoricalData(symbol, 300, 500);
                await this.page.waitForTimeout(500);
            }

            // بدء مراقبة دورية للبيانات
            this.startPeriodicDataRequests();

            this.logger.connection('✅ تم بدء عملية جلب البيانات الحقيقية بنجاح');

        } catch (error) {
            this.logger.error('❌ فشل في بدء عملية جلب البيانات:', error);
        }
    }

    /**
     * بدء طلبات دورية للبيانات
     */
    startPeriodicDataRequests() {
        // طلب معلومات الرصيد كل 10 ثوان
        this.balanceInterval = setInterval(async () => {
            try {
                await this.requestBalance();
            } catch (error) {
                this.logger.debug('خطأ في الطلب الدوري للرصيد:', error);
            }
        }, 10000);

        // طلب قائمة الأدوات كل 60 ثانية
        this.instrumentsInterval = setInterval(async () => {
            try {
                await this.requestInstrumentsList();
            } catch (error) {
                this.logger.debug('خطأ في الطلب الدوري للأدوات:', error);
            }
        }, 60000);

        // تحديث الاشتراك في الأسعار كل 30 ثانية
        this.quotesInterval = setInterval(async () => {
            try {
                await this.subscribeToQuotes();
            } catch (error) {
                this.logger.debug('خطأ في الطلب الدوري للأسعار:', error);
            }
        }, 30000);

        this.logger.connection('✅ تم بدء الطلبات الدورية للبيانات');
    }

    /**
     * إيقاف الطلبات الدورية
     */
    stopPeriodicDataRequests() {
        if (this.balanceInterval) {
            clearInterval(this.balanceInterval);
            this.balanceInterval = null;
        }

        if (this.instrumentsInterval) {
            clearInterval(this.instrumentsInterval);
            this.instrumentsInterval = null;
        }

        if (this.quotesInterval) {
            clearInterval(this.quotesInterval);
            this.quotesInterval = null;
        }

        this.logger.connection('⏹️ تم إيقاف الطلبات الدورية للبيانات');
    }

    /**
     * حفظ رسائل WebSocket في ملف
     */
    async saveWebSocketMessages(filePath) {
        const messages = await this.getWebSocketMessages();
        if (messages.length > 0) {
            await fs.writeFile(filePath, JSON.stringify(messages, null, 2));
            this.logger.connection(`تم حفظ ${messages.length} رسالة WebSocket`);
        }
    }

    /**
     * حفظ بيانات الجلسة
     */
    async saveSessionData(filePath) {
        if (this.sessionData) {
            await fs.writeFile(filePath, JSON.stringify(this.sessionData, null, 2));
            this.logger.connection('تم حفظ بيانات الجلسة');
        }
    }
}

module.exports = { QuotexConnection };
