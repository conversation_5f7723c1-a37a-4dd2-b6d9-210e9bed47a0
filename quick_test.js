/**
 * اختبار سريع لمكونات النظام
 */

console.log('🔍 اختبار سريع لمكونات النظام...\n');

// اختبار تحميل الوحدات
const tests = [
    {
        name: 'Config',
        path: './src/config/Config.js',
        test: async () => {
            const { Config } = require('./src/config/Config');
            const config = new Config();
            await config.load();
            return config.get('trading.defaultAmount') !== undefined;
        }
    },
    {
        name: 'Logger',
        path: './src/utils/Logger.js',
        test: async () => {
            const { Logger } = require('./src/utils/Logger');
            const logger = new Logger();
            logger.info('Test message');
            return true;
        }
    },
    {
        name: 'QuotexConnection',
        path: './src/connection/QuotexConnection.js',
        test: async () => {
            const { QuotexConnection } = require('./src/connection/QuotexConnection');
            const mockConfig = { get: (key) => key === 'connection.timeout' ? 30000 : null };
            const connection = new QuotexConnection(mockConfig, { info: () => {}, error: () => {}, connection: () => {} });
            return connection.getStatus() !== undefined;
        }
    },
    {
        name: 'DataManager',
        path: './src/data/DataManager.js',
        test: async () => {
            const { DataManager } = require('./src/data/DataManager');
            const mockConfig = { get: (key) => key === 'data.dataPath' ? './data' : null };
            const dataManager = new DataManager(mockConfig, { info: () => {}, error: () => {} });
            return dataManager.getStatus() !== undefined;
        }
    },
    {
        name: 'TradeManager',
        path: './src/trading/TradeManager.js',
        test: async () => {
            const { TradeManager } = require('./src/trading/TradeManager');
            const mockConfig = { get: (key) => key === 'trading.defaultAmount' ? 1 : null };
            const tradeManager = new TradeManager(mockConfig, { info: () => {}, error: () => {}, trade: () => {} });
            return tradeManager.getStatus() !== undefined;
        }
    },
    {
        name: 'StrategyManager',
        path: './src/strategy/StrategyManager.js',
        test: async () => {
            const { StrategyManager } = require('./src/strategy/StrategyManager');
            const mockConfig = { get: (key) => key === 'data.dataPath' ? './data' : {} };
            const strategyManager = new StrategyManager(mockConfig, { info: () => {}, error: () => {}, strategy: () => {} });
            return strategyManager.getStatus() !== undefined;
        }
    },
    {
        name: 'Trade',
        path: './src/trading/Trade.js',
        test: async () => {
            const { Trade } = require('./src/trading/Trade');
            const trade = new Trade({
                symbol: 'EURUSD',
                direction: 'call',
                amount: 1,
                expiration: 60,
                openPrice: 1.1000
            });
            return trade.isValid();
        }
    },
    {
        name: 'Helpers',
        path: './src/utils/helpers.js',
        test: async () => {
            const helpers = require('./src/utils/helpers');
            return helpers.formatCurrency(100) === '$100.00';
        }
    },
    {
        name: 'TradingBot',
        path: './src/core/TradingBot.js',
        test: async () => {
            const { TradingBot } = require('./src/core/TradingBot');
            const { Config } = require('./src/config/Config');
            const { Logger } = require('./src/utils/Logger');
            
            const config = new Config();
            await config.load();
            const logger = new Logger();
            const bot = new TradingBot(config, logger);
            
            return bot.getStatus() !== undefined;
        }
    }
];

async function runTests() {
    let passed = 0;
    let failed = 0;
    
    for (const test of tests) {
        try {
            console.log(`🧪 اختبار ${test.name}...`);
            const result = await test.test();
            
            if (result) {
                console.log(`✅ ${test.name}: نجح`);
                passed++;
            } else {
                console.log(`❌ ${test.name}: فشل`);
                failed++;
            }
        } catch (error) {
            console.log(`❌ ${test.name}: خطأ - ${error.message}`);
            failed++;
        }
    }
    
    console.log('\n📊 نتائج الاختبار:');
    console.log(`✅ نجح: ${passed}`);
    console.log(`❌ فشل: ${failed}`);
    console.log(`📈 معدل النجاح: ${((passed / tests.length) * 100).toFixed(1)}%`);
    
    if (failed === 0) {
        console.log('\n🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.');
        console.log('\n🚀 للتشغيل:');
        console.log('   node test_bot.js    # اختبار شامل');
        console.log('   npm start           # تشغيل الروبوت');
    } else {
        console.log('\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.');
    }
}

runTests().catch(console.error);
